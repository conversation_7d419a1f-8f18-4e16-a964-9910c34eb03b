# Copyright (C) 2025 DHL
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: DHL Shipping Germany for WooCommerce 3.9.4\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/dhl-for-woocommerce-DEV\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-08-10T17:23:37+02:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.12.0\n"
"X-Domain: dhl-for-woocommerce\n"

#. Plugin Name of the plugin
#: pr-dhl-woocommerce.php
msgid "DHL Shipping Germany for WooCommerce"
msgstr ""

#. Plugin URI of the plugin
#: pr-dhl-woocommerce.php
msgid "https://github.com/shadimanna/dhl-logistic-services-for-woocommerce"
msgstr ""

#. Description of the plugin
#: pr-dhl-woocommerce.php
msgid "WooCommerce integration for DHL Paket and Deutsche Post International"
msgstr ""

#. Author of the plugin
#: pr-dhl-woocommerce.php
msgid "DHL"
msgstr ""

#. Author URI of the plugin
#: pr-dhl-woocommerce.php
msgid "http://dhl.com/"
msgstr ""

#. translators: %s is the name of the service (e.g., DHL)
#: includes/abstract-pr-dhl-wc-order.php:90
#, php-format
msgid "%s Label & Tracking"
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:139
msgid "Delete Label"
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:142
#: includes/abstract-pr-dhl-wc-order.php:366
msgid "Generate Label"
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:150
#: includes/abstract-pr-dhl-wc-order.php:155
#: includes/abstract-pr-dhl-wc-order.php:336
#: includes/class-pr-dhl-wc-order-deutsche-post.php:696
msgid "Download Label"
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:176
msgid "Service"
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:180
msgid "Service selected:"
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:194
msgid "Weight"
msgstr ""

#. translators: %s is the weight unit (e.g., kg or lbs)
#: includes/abstract-pr-dhl-wc-order.php:200
#, php-format
msgid "Estimated shipment weight (%s) based on items ordered: "
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:224
msgid "There are no DHL services available for the destination country!"
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:335
#: includes/abstract-pr-dhl-wc-order.php:365
#: includes/class-pr-dhl-wc-order-deutsche-post.php:695
msgid "Your DHL label is ready to download, click the \"Download Label\" button above\""
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:360
msgid "There are no tracking number to delete."
msgstr ""

#. translators: %s is the tracking link
#: includes/abstract-pr-dhl-wc-order.php:405
#, php-format
msgid "%s Tracking Number: {tracking-link}"
msgstr ""

#. translators: %s is the return label number
#: includes/abstract-pr-dhl-wc-order.php:427
#, php-format
msgid ""
"\n"
" Return Label Number: %s"
msgstr ""

#. translators: %s is the order number
#: includes/abstract-pr-dhl-wc-order.php:1166
#, php-format
msgid "Order #%s: DHL label created"
msgstr ""

#. translators: %1$s is the order number, %2$s is the error message
#: includes/abstract-pr-dhl-wc-order.php:1179
#: includes/abstract-pr-dhl-wc-order.php:1290
#, php-format
msgid "Order #%1$s: %2$s"
msgstr ""

#. translators: %1$s and %2$s are HTML tags for the download link
#: includes/abstract-pr-dhl-wc-order.php:1202
#, php-format
msgid "Bulk DHL labels file created - %1$sdownload file%2$s"
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:1207
msgid "Could not create bulk DHL label file, download individually."
msgstr ""

#. translators: %s is the order number
#: includes/abstract-pr-dhl-wc-order.php:1283
#, php-format
msgid "Order #%s: DHL Label Deleted"
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:1333
#: includes/abstract-pr-dhl-wc-order.php:1352
msgid "There are no files to merge."
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:1339
msgid "The first file is empty."
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:1345
msgid "File format not supported."
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:1360
#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:624
msgid "Library conflict, could not merge PDF files. Please download PDF files individually."
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:1373
#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:639
msgid "Not all the file formats are the same."
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:1439
msgid "There are currently no bulk DHL label file to download or the download link for the bulk DHL label file has already expired. Please try again."
msgstr ""

#: includes/abstract-pr-dhl-wc-order.php:1462
#: includes/class-pr-dhl-wc-order-deutsche-post.php:1161
msgid "Unable to download file. Label appears to be invalid or is missing. Please try again."
msgstr ""

#: includes/abstract-pr-dhl-wc-product-editor.php:89
#: includes/abstract-pr-dhl-wc-product.php:74
msgid "- select country -"
msgstr ""

#: includes/abstract-pr-dhl-wc-product-editor.php:112
#: includes/abstract-pr-dhl-wc-product.php:93
msgid "HS Code"
msgstr ""

#: includes/abstract-pr-dhl-wc-product.php:28
#: includes/abstract-pr-dhl-wc-product.php:107
#: includes/class-pr-dhl-wc-product-editor-paket.php:23
msgid "Country of Manufacture (DHL)"
msgstr ""

#: includes/abstract-pr-dhl-wc-product.php:29
#: includes/abstract-pr-dhl-wc-product.php:120
#: includes/class-pr-dhl-wc-product-editor-paket.php:24
msgid "Harmonized Tariff Schedule (DHL)"
msgstr ""

#: includes/abstract-pr-dhl-wc-product.php:30
#: includes/class-pr-dhl-wc-product-editor-paket.php:25
msgid "Harmonized Tariff Schedule is a number assigned to every possible commodity that can be imported or exported from any country."
msgstr ""

#: includes/abstract-pr-dhl-wc-product.php:103
msgid "- No change -"
msgstr ""

#: includes/class-pr-dhl-extend-block-core.php:61
#: includes/front-end/class-pr-dhl-front-end-paket.php:508
msgid "Please enter the preferred location."
msgstr ""

#: includes/class-pr-dhl-extend-block-core.php:70
#: includes/front-end/class-pr-dhl-front-end-paket.php:513
msgid "Please enter the preferred neighbor name and address."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:15
msgid "Deutsche Post International"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:19
#, php-format
msgid "To start creating Deutsche Post shipping labels and return back a tracking number to your customers, please fill in your user credentials as provided by Deutsche Post. Not yet a customer? Please get a quote %1$shere%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:70
msgid "Deutsche Post Products not displaying - "
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:77
#: includes/class-pr-dhl-wc-method-paket.php:149
msgid "Account and API Settings"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:79
msgid "Please configure your account and API settings with Deutschepost International."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:86
#: includes/class-pr-dhl-wc-method-paket.php:187
#: includes/class-pr-dhl-wc-wizard-paket.php:81
msgid "Account Number (EKP)"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:88
msgid "Your account number (9; 10 or 15 digits, numerical), also called \"EKP“. This will be provided by your local Deutsche Post sales organization."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:95
msgid "Contact Name"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:97
msgid "Required for all customers. The name of the merchant, used as contact information on the Waybill."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:103
msgid "Contact Phone Number"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:105
msgid "Required for DHL Express customers. The phone number of the merchant, used as contact information on the Waybill."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:111
msgid "Client Id"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:113
msgid "The client ID (a 36 digits alphanumerical string made from 5 blocks) is required for authentication and is provided to you within your contract."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:121
msgid "Client Secret"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:123
msgid "The client secret (also a 36 digits alphanumerical string made from 5 blocks) is required for authentication (together with the client ID) and creates the tokens needed to ensure secure access. It is part of your contract provided by your Deutsche Post sales partner."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:131
#: includes/class-pr-dhl-wc-method-paket.php:196
msgid "Sandbox Mode"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:133
#: includes/class-pr-dhl-wc-method-paket.php:198
msgid "Enable Sandbox Mode"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:135
msgid "Please, tick here if you want to test the plug-in installation against the Deutsche Post Sandbox Environment. Labels generated via Sandbox cannot be used for shipping and you need to enter your client ID and client secret for the Sandbox environment instead of the ones for production!"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:147
msgid "Press the button for testing the connection against our Deutsche Post (depending on the selected environment this test is being done against the Sandbox or the Production Environment)."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:154
#: includes/class-pr-dhl-wc-method-paket.php:228
msgid "Debug Log"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:156
#: includes/class-pr-dhl-wc-method-paket.php:230
msgid "Enable logging"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:160
#, php-format
msgid "A log file containing the communication to the Deutsche Post server will be maintained if this option is checked. This can be used in case of technical issues and can be found %1$shere%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:169
msgid "Shipping"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:171
msgid "Please configure your shipping parameters underneath."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:175
#: includes/class-pr-dhl-wc-method-paket.php:318
msgid "International Default Service"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:177
msgid "Please select your default Deutsche Post shipping service for cross-border shippments that you want to offer to your customers (you can always change this within each individual order afterwards)."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:186
#: includes/class-pr-dhl-wc-method-paket.php:457
msgid "Additional Weight Type"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:188
#: includes/class-pr-dhl-wc-method-paket.php:459
msgid "Select whether to add an absolute weight amount or percentage amount to the total product weight."
msgstr ""

#. Translators: %s represents the weight unit (e.g., kg, lbs, %).
#: includes/class-pr-dhl-wc-method-deutsche-post.php:202
#: includes/class-pr-dhl-wc-method-paket.php:470
#, php-format
msgid "Additional Weight (%s or %%)"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:206
msgid "Add extra weight in addition to the products. Either an absolute amount or percentage (e.g. 10 for 10%)."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:230
#: includes/class-pr-dhl-wc-method-deutsche-post.php:241
#: includes/class-pr-dhl-wc-method-paket.php:526
msgid "Tracking Note"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:232
#: includes/class-pr-dhl-wc-method-paket.php:528
msgid "Make Private"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:234
#: includes/class-pr-dhl-wc-method-paket.php:530
msgid "Please, tick here to not send an email to the customer when the tracking number is added to the order."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:243
msgid "Set the custom text when adding the tracking number to the order notes. {tracking-link} is where the tracking number will be set."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:248
msgid "Deutsche Post Tracking Number: {tracking-link}"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:254
msgid "Label options"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:256
msgid "Options for configuring your label preferences"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:259
msgid "Label Reference"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:264
#: includes/class-pr-dhl-wc-method-deutsche-post.php:277
#, php-format
msgid "Use \"%1$s\" to send the order id as a reference and \"%2$s\" to send the customer email. This text is limited to 35 characters."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:272
msgid "Label Reference 2"
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:363
msgid "Contact Phone Number required, please add in settings."
msgstr ""

#: includes/class-pr-dhl-wc-method-deutsche-post.php:386
msgid "Could not reset connection: "
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:28
#: includes/class-pr-dhl-wc-method-paket.php:1063
#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:169
#: includes/pr-dhl-api/class-pr-dhl-api-rest-paket.php:305
msgid "DHL Paket"
msgstr ""

#. translators: %s: link to request a quote for becoming a DHL business customer
#: includes/class-pr-dhl-wc-method-paket.php:31
#, php-format
msgid "Below you will find all functions for controlling, preparing and processing your shipment with DHL Paket. Prerequisite is a valid DHL business customer contract. If you are not yet a DHL business customer, you can request a quote %1$shere%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:105
#: templates/checkout/dhl-preferred-services.php:127
msgid "None"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:112
msgid "- Select DHL Product -"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:115
msgid "Product Name"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:116
msgid "Product Categories"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:117
msgid "Product Tags"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:127
msgid "DHL Products not displaying - "
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:134
#, php-format
msgid "<p style=\"color: red;\">Your password will expire in less than 7 days, please go to your <a href=\"%s\" target=\"_blank\">business portal</a> and reset your password then click the \"Get Account Settings\" button below.</p>"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:140
#, php-format
msgid "<p style=\"color: red;\">Your password will expire in less than 30 days, please go to your <a href=\"%s\" target=\"_blank\">business portal</a> and reset your password then click the \"Get Account Settings\" button below.</p>"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:151
msgid "Please configure your shipping parameters and your access towards the DHL Paket APIs by means of authentication."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:154
#: includes/class-pr-dhl-wc-wizard-paket.php:106
msgid "Username"
msgstr ""

#. Translators: %s is the URL for the DHL business customer portal.
#: includes/class-pr-dhl-wc-method-paket.php:158
#, php-format
msgid "Your username for the DHL business customer portal. Please note the lower case and test your access data in advance at %1$shere%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:166
#: includes/class-pr-dhl-wc-wizard-paket.php:109
msgid "Password"
msgstr ""

#. Translators: %s is the URL for the DHL business customer portal.
#: includes/class-pr-dhl-wc-method-paket.php:170
#, php-format
msgid "Your password for the DHL business customer portal. Please note the new assignment of the password to 3 (Standard User) or 12 (System User) months and test your access data in advance at %1$shere%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:183
#: includes/class-pr-dhl-wc-method-paket.php:250
msgid "Press the button to read your DHL Business Account settings into the DHL for WooCommerce plugin."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:189
msgid "Your DHL account number (10 digits - numerical), also called \"EKP“. This will be provided by your local DHL sales organization."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:200
msgid "Please, tick here if you want to test the plug-in installation against the DHL Sandbox Environment. Labels generated via Sandbox cannot be used for shipping and you need to enter your client ID and client secret for the Sandbox environment instead of the ones for production!"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:204
msgid "Sandbox Username"
msgstr ""

#. Translators: %s is the URL for creating a DHL developer portal account.
#: includes/class-pr-dhl-wc-method-paket.php:208
#, php-format
msgid "Your sandbox username is the same as for the DHL developer portal. You can create an account %1$shere%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:216
msgid "Sandbox Password"
msgstr ""

#. Translators: %s is the URL for creating a DHL developer portal account.
#: includes/class-pr-dhl-wc-method-paket.php:220
#, php-format
msgid "Your sandbox password is the same as for the DHL developer portal. You can create an account %1$shere%2$s."
msgstr ""

#. Translators: %s is the URL to the log file.
#: includes/class-pr-dhl-wc-method-paket.php:234
#, php-format
msgid "A log file containing the communication to the DHL server will be maintained if this option is checked. This can be used in case of technical issues and can be found %1$shere%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:240
msgid "DHL Products and Participation Number"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:242
msgid "For each DHL product that you would like to use, please enter your participation number here. The participation number consists of the last two characters of the respective accounting number, which you will find in your DHL contract data (for example, 01)."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:298
msgid "DHL Retoure"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:304
msgid "Shipping Label Settings"
msgstr ""

#. translators: %1$s: link to upload logo; %2$s: closing tag for link
#: includes/class-pr-dhl-wc-method-paket.php:307
#, php-format
msgid "Would you like to customize the DHL shipment notification? You can now add your online shop’s name and logo and we will display it in the DHL shipment notification. To upload your logo please use the following %1$slink%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:310
msgid "Domestic Default Service"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:312
msgid "Please select your default DHL Paket shipping service for domestic shippments that you want to offer to your customers (you can always change this within each individual order afterwards)"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:320
msgid "Please select your default DHL Paket shipping service for cross-border shippments that you want to offer to your customers (you can always change this within each individual order afterwards)."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:326
msgid "Send Customer Email"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:328
msgid "Please select whether to send the customer's email to DHL or not. \"Customer Confirmation\" displays a confirmation on the checkout page and \"Confirmed via terms & condition\" assumes confirmation via the website terms & conditions."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:330
#: includes/class-pr-dhl-wc-method-paket.php:342
msgid "Do not send"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:331
msgid "Customer confirmation"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:332
#: includes/class-pr-dhl-wc-method-paket.php:343
msgid "Confirmed via terms & condition"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:338
msgid "Send Customer Phone"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:340
msgid "Please select whether to send the customer's phone to DHL or not. \"Confirmed via terms & condition\" assumes confirmation via the website terms & conditions."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:349
msgid "Visual Age Check default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:352
msgid "Please, tick here if you want the \"Visual Age Check\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:357
msgid "Additional Insurance default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:359
#: includes/class-pr-dhl-wc-method-paket.php:367
#: includes/class-pr-dhl-wc-method-paket.php:375
#: includes/class-pr-dhl-wc-method-paket.php:383
#: includes/class-pr-dhl-wc-method-paket.php:391
#: includes/class-pr-dhl-wc-method-paket.php:399
#: includes/class-pr-dhl-wc-method-paket.php:407
#: includes/class-pr-dhl-wc-method-paket.php:415
#: includes/class-pr-dhl-wc-method-paket.php:435
#: includes/class-pr-dhl-wc-method-paket.php:443
#: includes/class-pr-dhl-wc-method-paket.php:451
#: includes/class-pr-dhl-wc-method-paket.php:845
msgid "Checked"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:361
msgid "Please, tick here if you want the \"Additional Insurance\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:365
msgid "No Neighbor Delivery default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:369
msgid "Please, tick here if you want the \"No Neighbor Delivery\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:373
msgid "Named Person Only default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:377
msgid "Please, tick here if you want the \"Named Person Only\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:381
msgid "Premium default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:385
msgid "Please, tick here if you want the \"Premium\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:389
msgid "Bulky Goods default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:393
msgid "Please, tick here if you want the \"Bulky Goods\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:397
msgid "Ident Check default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:401
#: includes/class-pr-dhl-wc-method-paket.php:418
msgid "Please, tick here if you want the \"Ident Check\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:405
msgid "Signed for by recipient default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:409
msgid "Please, tick here if you want the \"Signed for by recipient\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:413
msgid "Ident Check Age default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:422
msgid "Endorsement type"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:426
#: includes/class-pr-dhl-wc-order-paket.php:521
msgid "Sending back to sender"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:427
#: includes/class-pr-dhl-wc-order-paket.php:522
msgid "Abandonment of parcel"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:429
msgid "Please, tick here if you want the \"Endorsement value\" to be selected in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:433
msgid "Print Only If Codeable default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:437
msgid "Please, tick here if you want the \"Print Only If Codeable\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:441
msgid "Parcel Outlet Routing default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:445
msgid "Please, tick here if you want the \"Parcel Outlet Routing\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:449
msgid "GoGreen Plus default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:453
msgid "Check to request GoGreen Plus for every domestic shipment unless you turn it off per order."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:474
msgid "Add extra weight in addition to the products.  Either an absolute amount or percentage (e.g. 10 for 10%)."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:481
msgid "Package Description"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:483
msgid "Prefill the customs package description with one of the options for cross-border packages."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:490
msgid "Label Format"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:492
msgid "Select one of the formats to generate the shipping label in."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:510
msgid "Logo"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:512
msgid "Add Logo"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:514
msgid "The logo will be added from your DHL dashboard settings."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:518
msgid "Shipper Reference"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:520
msgid "Add shipper reference."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:534
msgid "Tracking Text"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:536
msgid "Set the custom text when adding the tracking number to the order notes or completed email. {tracking-link} is where the tracking number will be set."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:538
msgid "DHL Tracking Number: {tracking-link}"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:541
msgid "Tracking Email"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:543
msgid "Add tracking text in completed email"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:544
msgid "Please, tick here to add tracking text when completed email is sent."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:550
msgid "Tracking URL Language"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:552
msgid "Select the tracking link language."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:553
msgid "Select language of the tracking link page."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:563
msgid "Create Label on Status"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:565
msgid "Create label on specific status."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:566
msgid "Select the order status."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:573
msgid "Order Status"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:575
msgid "Change to Completed"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:576
msgid "Please, tick here to change the order status when label is generated."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:589
msgid "Preferred Service"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:591
msgid "Preferred service options."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:594
#: templates/checkout/dhl-closest-drop-point.php:39
msgid "Closest Drop Point"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:596
msgid "Enable Closest Drop Point"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:597
msgid "Enabling this will display a front-end option for the user to select delivery option (Home address or CDP delivery)."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:601
#: includes/front-end/class-pr-dhl-front-end-paket.php:37
msgid "Delivery Day"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:603
msgid "Enable Delivery Day"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:604
msgid "Enabling this will display a front-end option for the user to select their preferred day of delivery."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:608
msgid "Delivery Day Price"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:610
msgid "Insert gross value as surcharge for the preferred day. Insert 0 to offer service for free."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:616
msgid "Cut Off Time"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:618
msgid "The cut-off time is the latest possible order time up to which the minimum preferred day (day of order + 2 working days) can be guaranteed. As soon as the time is exceeded, the earliest preferred day displayed in the frontend will be shifted to one day later (day of order + 3 working days)."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:623
msgid "Exclusion of transfer days"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:625
#: includes/front-end/class-pr-dhl-front-end-paket.php:229
msgid "Monday"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:626
msgid "Exclude days to transfer packages to DHL."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:631
#: includes/front-end/class-pr-dhl-front-end-paket.php:230
msgid "Tuesday"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:635
#: includes/front-end/class-pr-dhl-front-end-paket.php:231
msgid "Wednesday"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:639
#: includes/front-end/class-pr-dhl-front-end-paket.php:232
msgid "Thursday"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:643
#: includes/front-end/class-pr-dhl-front-end-paket.php:233
msgid "Friday"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:647
#: includes/front-end/class-pr-dhl-front-end-paket.php:234
msgid "Saturday"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:650
msgid "Preferred Location"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:652
msgid "Enable Preferred Location"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:653
msgid "Enabling this will display a front-end option for the user to select their preferred location."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:657
msgid "Preferred Neighbour"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:659
msgid "Enable Preferred Neighbour"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:660
msgid "Enabling this will display a front-end option for the user to select their preferred neighbour."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:664
msgid "Shipping Methods"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:666
msgid "Select the Shipping Methods to display the enabled DHL Paket preferred services and Location Finder below. You can press \"ctrl\" to select multiple options or click on a selected option to deselect it."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:672
msgid "Exclude Payment Gateways"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:675
msgid "Select the Payment Gateways to hide the enabled DHL Paket preferred services and Location Finder below. You can press \"ctrl\" to select multiple options or click on a selected option to deselect it."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:681
msgid "COD Payment Gateways"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:684
msgid "Select the Payment Gateways to use with DHL COD services. You can press \"ctrl\" to select multiple options or click on a selected option to deselect it."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:690
msgid "Location Finder"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:692
msgid "Please define the parameters for the display of dhl locations in the shop frontend."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:695
#: includes/front-end/class-pr-dhl-front-end-paket.php:635
#: templates/checkout/dhl-parcel-finder.php:34
msgid "Packstation"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:697
msgid "Enable Packstation"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:698
msgid "Enabling this will display Packstation locations on Google Maps when searching for drop off locations on the front-end."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:702
#: includes/class-pr-dhl-wc-method-paket.php:1151
msgid "Parcelshop"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:704
msgid "Enable Parcelshop"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:705
msgid "Enabling this will display Parcelshop locations on Google Maps when searching for drop off locations on the front-end."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:709
#: includes/class-pr-dhl-wc-method-paket.php:1160
msgid "Post Office"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:711
msgid "Enable Post Office"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:712
msgid "Enabling this will display Post Office locations on Google Maps when searching for drop off locations on the front-end."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:716
msgid "Map"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:718
msgid "Enable Map"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:719
msgid "Enabling this will display the Map on the front-end."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:723
msgid "Map type"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:725
msgid "Select the map type to show parcels shops."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:732
msgid "Limit Results"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:734
msgid "Limit displayed results, from 1 to at most 50."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:744
msgid "API Key"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:749
#, php-format
msgid "The Google Maps API Key is necessary to display the DHL Locations on a Google map.%1$sGet a free Google Maps API key %2$shere%3$s."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:770
msgid "Shipper Address / Pickup Request Address"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:772
msgid "Enter Shipper Address. This address is also used for Pickup Requests.<br/>Note: For pickup requests to be accepted, this address must match a pickup address saved to your DHL Portal."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:775
#: includes/class-pr-dhl-wc-method-paket.php:851
#: includes/class-pr-dhl-wc-wizard-paket.php:142
msgid "Name"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:777
msgid "Enter Shipper Name."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:782
#: includes/class-pr-dhl-wc-method-paket.php:858
#: includes/class-pr-dhl-wc-wizard-paket.php:145
msgid "Company"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:784
msgid "Enter Shipper Company."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:789
#: includes/class-pr-dhl-wc-method-paket.php:865
#: includes/class-pr-dhl-wc-wizard-paket.php:148
msgid "Street Address"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:791
msgid "Enter Shipper Street Address."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:796
#: includes/class-pr-dhl-wc-method-paket.php:872
#: includes/class-pr-dhl-wc-wizard-paket.php:151
msgid "Street Address Number"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:798
msgid "Enter Shipper Street Address Number."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:803
#: includes/class-pr-dhl-wc-method-paket.php:879
#: includes/class-pr-dhl-wc-wizard-paket.php:154
#: templates/checkout/dhl-parcel-finder.php:23
msgid "City"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:805
msgid "Enter Shipper City."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:810
#: includes/class-pr-dhl-wc-method-paket.php:886
#: includes/class-pr-dhl-wc-wizard-paket.php:157
msgid "State"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:812
msgid "Enter Shipper County."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:817
#: includes/class-pr-dhl-wc-method-paket.php:893
#: includes/class-pr-dhl-wc-wizard-paket.php:160
msgid "Postcode"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:819
msgid "Enter Shipper Postcode."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:824
#: includes/class-pr-dhl-wc-method-paket.php:900
msgid "Phone Number"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:826
#: includes/class-pr-dhl-wc-method-paket.php:902
msgid "Enter Phone Number."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:831
#: includes/class-pr-dhl-wc-method-paket.php:907
#: includes/class-pr-dhl-wc-wizard-paket.php:166
msgid "Email"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:833
#: includes/class-pr-dhl-wc-method-paket.php:909
msgid "Enter Email."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:838
#: includes/class-pr-dhl-wc-order-paket.php:96
msgid "Return Address"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:840
msgid "Enter Return Address below."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:843
msgid "Create Return Label default"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:847
msgid "Please, tick here if you want the \"Create Return Label\" option to be checked in the \"Edit Order\" before printing a label."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:853
msgid "Enter Return Name."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:860
msgid "Enter Return Company."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:867
msgid "Enter Return Street Address."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:874
msgid "Enter Return Street Address Number."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:881
msgid "Enter Return City."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:888
msgid "Enter Return County."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:895
msgid "Enter Return Postcode."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:914
msgid "Bank Details"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:916
msgid "Enter your bank details needed for services that use COD."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:919
msgid "Account Owner"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:923
msgid "Bank Name"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:927
msgid "IBAN"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:931
msgid "BIC"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:936
msgid "Payment Reference"
msgstr ""

#. translators: %1$s: order ID placeholder, %2$s: customer email placeholder
#: includes/class-pr-dhl-wc-method-paket.php:940
#: includes/class-pr-dhl-wc-method-paket.php:949
#, php-format
msgid "Use \"%1$s\" to send the order id as a bank reference and \"%2$s\" to send the customer email. This text is limited to 35 characters."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:945
msgid "Payment Reference 2"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:967
msgid "Business Hours (for DHL Pickup Request)"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:969
msgid "The business hours available for DHL Pickup."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:972
msgid "From: "
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:977
msgid "To: "
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:982
msgid "(Additional Business Hours) From: "
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:984
#: includes/class-pr-dhl-wc-method-paket.php:990
msgid "Optional, if additional business hours are needed."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:988
msgid "(Additional Business Hours) To: "
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:1060
msgid "Method Title"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:1062
msgid "This controls the title which the user sees during checkout."
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:1082
msgid "Pickup Account Number: "
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:1104
msgid "Distribution Center: "
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:1169
msgid "Google Maps"
msgstr ""

#: includes/class-pr-dhl-wc-method-paket.php:1187
msgid "In order to use logo, you need to set a shipper reference first."
msgstr ""

#. translators: %s: type of location (e.g., DHL locations)
#: includes/class-pr-dhl-wc-method-paket.php:1214
#, php-format
msgid "In order to show %s, you need to set a Google API Key first."
msgstr ""

#. translators: %s: type of location (e.g., DHL locations on a map)
#: includes/class-pr-dhl-wc-method-paket.php:1217
#, php-format
msgid "In order to show %s on a map, you need to set a Google API Key first."
msgstr ""

#: includes/class-pr-dhl-wc-notice-legacy-parcel.php:68
msgid "DHL Parcel for WooCommerce notice"
msgstr ""

#. translators: %1$s is the link to install the new plugin, %2$s is the closing HTML tag for the link
#: includes/class-pr-dhl-wc-notice-legacy-parcel.php:72
#, php-format
msgid "DHL Parcel services are no longer available in this plugin. To continue using Parcel services, please install the new plugin %1$shere%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-notice-legacy-parcel.php:81
msgid "Click here to never show this again"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:112
msgid "AWB Copy Count"
msgstr ""

#. translators: %s is the name of the service (e.g., DHL Express, DHL Parcel)
#: includes/class-pr-dhl-wc-order-deutsche-post.php:176
#, php-format
msgid "%s Waybill"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:214
msgid "Sender customs reference: "
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:226
msgid "Importer customs reference: "
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:243
msgid "Contents Type:"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:325
msgid "Finalizing an order cannot be undone, so make sure you have added all the desired items before continuing."
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:401
msgid "There are no items in your Waybill"
msgstr ""

#. translators: %s is the order number
#: includes/class-pr-dhl-wc-order-deutsche-post.php:408
#: includes/class-pr-dhl-wc-order-deutsche-post.php:496
#, php-format
msgid "Order #%d"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:424
msgid "Remove"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:443
#: includes/class-pr-dhl-wc-order-deutsche-post.php:519
msgid "Item"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:444
msgid "Actions"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:452
msgid "AWB Copy Count:"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:464
msgid "Add to Waybill"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:468
msgid "Finalize Waybill"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:472
msgid "Please generate a label before adding the item to the Waybill"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:520
msgid "Barcode"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:529
msgid "Download Waybill"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:890
msgid "DHL Status"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:891
msgid "DHL Tracking"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:966
msgid "Waybill created"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:969
msgid "Added to waybill"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:972
msgid "Label created"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:980
msgid "Create DHL Label"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:981
msgid "Finalize DHL Waybill"
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:992
#: includes/class-pr-dhl-wc-order-deutsche-post.php:999
#: includes/class-pr-dhl-wc-order-paket.php:1013
msgid "No orders selected for the DHL bulk action, please select orders before performing the DHL action."
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:1010
msgid "One or more orders do not have a DHL item label created. Please ensure all DHL labels are created before adding them to the order."
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:1021
msgid "Copy count must not be empty."
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:1025
msgid "Copy count must be numeric."
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:1028
msgid "Copy count must not be more than 50."
msgstr ""

#: includes/class-pr-dhl-wc-order-deutsche-post.php:1105
msgid "download file"
msgstr ""

#. translators: %1$s is the order type (e.g., finalized or pending), %2$s is the date or other relevant detail
#: includes/class-pr-dhl-wc-order-deutsche-post.php:1110
#, php-format
msgid "Finalized DHL Waybill for %1$s orders - %2$s"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:76
msgid "COD"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:81
msgid "COD Amount:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:101
msgid "Create return label: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:112
msgid "Name:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:123
msgid "Company:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:134
msgid "Street Address:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:145
msgid "Street Address Number:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:156
msgid "City:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:167
msgid "State:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:178
msgid "Postcode:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:189
msgid "Phone:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:200
msgid "Email:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:227
#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:287
msgid "none"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:230
msgid "Delivery Options"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:235
msgid "Delivery Day:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:248
msgid "Preferred Location (80 characters max): "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:266
msgid "Preferred Neighbor (80 characters max): "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:281
#: includes/class-pr-dhl-wc-order-paket.php:509
msgid "Additional Services"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:288
msgid "Visual Age Check:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:308
msgid "No Neighbour Delivery: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:319
msgid "Named Person Only: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:330
msgid "Signed for by recipient: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:344
msgid "Ident-Check: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:373
msgid "Ident-Check - Date of Birth: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:386
msgid "Ident-Check - Minimum Age: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:399
msgid "Print Only If Codeable: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:412
msgid "Parcel Outlet Routing: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:423
msgid "Parcel Outlet Routing - Email: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:434
msgid "GoGreen Plus:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:449
msgid "Crossborder"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:457
msgid "Postal Delivered Duty Paid::"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:471
msgid "Duties:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:483
msgid "Invoice Number:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:494
msgid "Movement Reference Number (MRN):"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:517
msgid "Endorsement:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:537
#: includes/class-pr-dhl-wc-order-paket.php:595
msgid "Closest drop-point delivery: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:547
msgid "Email Notification:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:557
msgid "Additional Insurance:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:568
msgid "Premium: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:582
msgid "Bulky Goods: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:623
msgid "Multiple Packages"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:628
msgid "Send multiple packages: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:640
msgid "Total Packages:"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:997
msgid "DHL Create Labels"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:998
msgid "DHL Delete Labels"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:999
msgid "DHL Request Pickup"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1109
msgid "DHL Label Created"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1110
msgid "DHL Tracking Number"
msgstr ""

#. Translators: %s is the scheduled pickup date.
#: includes/class-pr-dhl-wc-order-paket.php:1233
#, php-format
msgid "DHL pickup scheduled for %s"
msgstr ""

#. Translators: %s is a list of order numbers for which the DHL pickup request was created.
#: includes/class-pr-dhl-wc-order-paket.php:1247
#, php-format
msgid "DHL Pickup Request created for Order(s): %s "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1257
msgid "Error message detail is not exist!"
msgstr ""

#. Translators: %s is the error message returned from the DHL pickup request.
#. Translators: %s is the error message returned from the DHL Pickup Request exception.
#: includes/class-pr-dhl-wc-order-paket.php:1264
#: includes/class-pr-dhl-wc-order-paket.php:1274
#, php-format
msgid "DHL Pickup Request error: %s"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1411
msgid "Schedule a DHL Pickup Request."
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1412
msgid "Your Shipper address and business hours from Settings will be used for the pickup."
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1435
msgid "Request Pickup: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1441
msgid "Pickup ASAP"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1442
msgid "Pickup Date"
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1452
msgid "Pickup Date: "
msgstr ""

#: includes/class-pr-dhl-wc-order-paket.php:1466
msgid "Submit"
msgstr ""

#: includes/class-pr-dhl-wc-product-deutsche-post.php:22
#: includes/class-pr-dhl-wc-product-editor-deutsche-post.php:23
msgid "Country of origin (Deutsche Post International)"
msgstr ""

#: includes/class-pr-dhl-wc-product-deutsche-post.php:23
#: includes/class-pr-dhl-wc-product-editor-deutsche-post.php:24
msgid "Harmonized Tariff code (Deutsche Post International)"
msgstr ""

#: includes/class-pr-dhl-wc-product-deutsche-post.php:24
#: includes/class-pr-dhl-wc-product-editor-deutsche-post.php:25
msgid "Optional information for non-EU shipments. Appears on CN22 (Deutsche Post International)."
msgstr ""

#: includes/class-pr-dhl-wc-product-deutsche-post.php:28
#: includes/class-pr-dhl-wc-product-editor-deutsche-post.php:22
msgid "Country of origin of goods. Mandatory for all non-EU shipments. Appears on CN22 (Deutsche Post International)."
msgstr ""

#: includes/class-pr-dhl-wc-product-deutsche-post.php:38
msgid "- select content indicator -"
msgstr ""

#: includes/class-pr-dhl-wc-product-deutsche-post.php:65
#: includes/class-pr-dhl-wc-product-editor-deutsche-post.php:42
msgid "Content description (Deutsche Post International)"
msgstr ""

#: includes/class-pr-dhl-wc-product-deutsche-post.php:66
#: includes/class-pr-dhl-wc-product-editor-deutsche-post.php:44
msgid "Description of goods (max 33 characters). Mandatory for all non-EU shipments. Appears on CN22 (Deutsche Post International)."
msgstr ""

#: includes/class-pr-dhl-wc-product-editor-paket.php:22
#: includes/class-pr-dhl-wc-product-paket.php:20
msgid "Country of Manufacture"
msgstr ""

#: includes/class-pr-dhl-wc-product-editor-paket.php:42
#: includes/class-pr-dhl-wc-product-paket.php:32
msgid "Cannot Transfer On Day Of Order (DHL)"
msgstr ""

#: includes/class-pr-dhl-wc-product-editor-paket.php:44
#: includes/class-pr-dhl-wc-product-paket.php:33
msgid "This product cannot be transfered to DHL on the same day of the order.  Checking this disables preferred services on the checkout page."
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:39
msgid "Welcome to DHL plugin! You're almost there, but we think this wizard might help you setup the plugin."
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:40
msgid "Run wizard"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:40
msgid "dismiss"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:70
msgid "Quick Set Up"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:72
msgid "Thank you for installing this plugin. We'll guide you through the set up and minimum required fields."
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:76
msgid "Begin Setup"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:83
msgid "Your DHL account number (10 digits - numerical), also called \"EKP\". This will be provided by your local DHL sales organization."
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:86
msgid "Enter your EKP"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:89
#: includes/class-pr-dhl-wc-wizard-paket.php:112
#: includes/class-pr-dhl-wc-wizard-paket.php:132
#: includes/class-pr-dhl-wc-wizard-paket.php:169
msgid "Next"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:94
msgid "API Settings"
msgstr ""

#. translators: %1$s & %2$s: link to DHL business portal
#: includes/class-pr-dhl-wc-wizard-paket.php:99
#, php-format
msgid "Please configure your access to the DHL Paket APIs by means of authentication. Your username for the DHL business customer portal is required. Please note the lowercase letters and test your access data in advance at %1$shere%2$s."
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:117
msgid "Participation Number"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:119
msgid "The participation number consists of the last two characters of the respective accounting number, which you will find in your DHL contract data (for example, 01)."
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:122
msgid "Regular product"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:137
msgid "Shipper Address"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:139
msgid "Enter Shipper Address. This address is also used for Pickup Requests."
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:163
msgid "Phone"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:174
msgid "All set"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:176
msgid "You can find all additional settings under WooCommerce > Settings > Shipping > DHL Paket"
msgstr ""

#: includes/class-pr-dhl-wc-wizard-paket.php:182
msgid "Finish Setup"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:38
msgid "Preferred Location or Neighbor"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:39
msgid "Preferred Location Address"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:40
msgid "Preferred Neighbor Name"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:41
msgid "Preferred Neighbor Address"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:45
#: templates/checkout/dhl-preferred-services.php:141
msgid "Location"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:46
#: templates/checkout/dhl-preferred-services.php:155
msgid "Neighbor"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:50
#: templates/checkout/dhl-closest-drop-point.php:19
msgid "Delivery option"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:118
#, php-format
msgid "For deliveries to DHL Parcel Lockers you have to %1screate a DHL account%2s and get a Post Number."
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:228
msgid "Opening Times"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:235
msgid "Sunday"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:236
msgid "Services"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:237
msgid "Yes"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:238
msgid "No"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:239
msgid "Parking"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:240
msgid "Handicap Accessible"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:244
#: includes/front-end/class-pr-dhl-front-end-paket.php:627
#: templates/checkout/dhl-parcel-finder.php:40
msgid "Branch"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:245
msgid "Select "
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:246
#: includes/front-end/class-pr-dhl-front-end-paket.php:834
msgid "Select a drop-off point"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:247
msgid "Post Number "
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:248
msgid "<span class=\"dhl-tooltip\" title=\"Indicate a preferred time, which suits you best for your parcel delivery by choosing one of the displayed time windows.\">?</span>"
msgstr ""

#. Translators: %1$s is an opening HTML tag and %2$s is a closing HTML tag for styling the error message.
#: includes/front-end/class-pr-dhl-front-end-paket.php:250
#, php-format
msgid "%1$sPlease insert an API Key to enable the display of locations in the frontend on a map.%2$s"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:251
msgid "Please enter a postcode to search locations."
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:262
msgid "Ship to a different address or Packstation/Branch?"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:328
msgid "No shipping method enabled."
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:363
#: includes/front-end/class-pr-dhl-front-end-paket.php:369
msgid "Payment gateway excluded."
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:377
msgid "Not enabled for selected shipping method."
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:475
msgid "DHL Delivery Day"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:632
msgid " / "
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:638
msgid "Search "
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:683
msgid "No parcel shops found"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:724
msgid "No Parcel Shops found. Ensure \"Packstation\" or Branch\" filter is checked "
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:822
msgid "Regular Address"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:826
msgid "DHL Packstation"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:830
msgid "DHL Branch"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:838
msgid "Address Type"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:848
#: includes/front-end/class-pr-dhl-front-end-paket.php:897
msgid "Post Number"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:856
msgid "Drop off points"
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:920
msgid "Activate Shipment Notification. When activated DHL will inform you via email about the shipment status of your order."
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:994
msgid "\"DHL Locations\" cannot be used - "
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:1002
msgid "Post Number is mandatory for a Packstation location."
msgstr ""

#. Translators: %s is the text that must be included in the address (e.g., a specific keyword or phrase).
#: includes/front-end/class-pr-dhl-front-end-paket.php:1008
#: includes/front-end/class-pr-dhl-front-end-paket.php:1014
#: includes/front-end/class-pr-dhl-front-end-paket.php:1020
#, php-format
msgid "The text \"%s\" must be included in the address."
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:1028
msgid "Post Number must be a number."
msgstr ""

#: includes/front-end/class-pr-dhl-front-end-paket.php:1034
msgid "The post number you entered is not valid. Please correct the number."
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:199
#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:264
#: includes/pr-dhl-api/class-pr-dhl-api-rest-finder.php:135
msgid "400 - "
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:202
#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:267
#: includes/pr-dhl-api/class-pr-dhl-api-rest-finder.php:138
msgid "401 - Unauthorized Access - Invalid token or Authentication Header parameter"
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:205
#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:270
#: includes/pr-dhl-api/class-pr-dhl-api-rest-finder.php:141
msgid "408 - Request Timeout"
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:208
#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:273
#: includes/pr-dhl-api/class-pr-dhl-api-rest-finder.php:144
msgid "429 - Too many requests in given amount of time"
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:211
#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:276
#: includes/pr-dhl-api/class-pr-dhl-api-rest-finder.php:147
msgid "503 - Service Unavailable"
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:215
msgid "POST error or timeout occured. Please try again later."
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:263
msgid "Unknown Error!"
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:280
#: includes/pr-dhl-api/class-pr-dhl-api-rest-finder.php:151
msgid "GET error or timeout occured. Please try again later."
msgstr ""

#. translators: %s is the required number of characters
#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:349
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:565
#, php-format
msgid "The value must be %s characters."
msgstr ""

#. translators: %1$s is the minimum number of characters, %2$s is the maximum number of characters
#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:352
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:568
#, php-format
msgid "The value must be between %1$s and %2$s characters."
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api-rest.php:358
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:574
msgid "The value must be a number"
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api.php:36
msgid "Parcel Finder not available"
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api.php:66
msgid "Delivery Duty Unpaid"
msgstr ""

#: includes/pr-dhl-api/abstract-pr-dhl-api.php:67
msgid "Delivery Duty Paid"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-auth-rest.php:147
msgid "The \"Cliend Id\" or \"Client Secret\" is empty."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-auth-rest.php:176
msgid "Authentication failed: Please, check client ID and secret in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:296
msgid "Packet Standard"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:297
msgid "Packet Priority"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:298
msgid "Packet Plus"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:299
msgid "Packet Tracked"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:365
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:120
msgid "DHL Label has no path!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:374
#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:61
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:131
msgid "DHL Label could not be deleted!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:525
#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:99
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:152
msgid "Invalid file path!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:531
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:159
msgid "DHL label file cannot be saved!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:561
msgid "DHL AWB Label could not be deleted!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:703
msgid "Shipment AWB: "
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:724
msgid "Sale Goods"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:725
msgid "Return Goods"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:726
msgid "Gift"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:727
msgid "Commercial Sample"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:728
msgid "Documents"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:729
msgid "Mixed Contents"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-deutsche-post.php:730
msgid "Others"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-factory.php:85
msgid "The DHL plugin is not supported in your store's \"Base Location\""
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:146
#: includes/pr-dhl-api/class-pr-dhl-api-rest-paket.php:328
msgid "DHL Paket Connect"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:147
#: includes/pr-dhl-api/class-pr-dhl-api-rest-paket.php:329
msgid "DHL Europaket (B2B)"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:148
#: includes/pr-dhl-api/class-pr-dhl-api-rest-paket.php:330
msgid "DHL Paket International"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:149
#: includes/pr-dhl-api/class-pr-dhl-api-rest-paket.php:331
msgid "DHL Warenpost International"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:170
#: includes/pr-dhl-api/class-pr-dhl-api-rest-paket.php:306
msgid "DHL Paket PRIO"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:171
#: includes/pr-dhl-api/class-pr-dhl-api-rest-paket.php:307
msgid "DHL Warenpost National"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:172
#: includes/pr-dhl-api/class-pr-dhl-api-rest-paket.php:308
msgid "DHL Kleinpaket"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:190
msgid "sun"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:265
msgid "Mon"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:266
msgid "Tue"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:267
msgid "Wed"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:268
msgid "Thu"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:269
msgid "Fri"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:270
msgid "Sat"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:271
msgid "Sun"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:297
msgid "Delivery Duty Paid (excl. VAT )"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:298
msgid "Delivery Duty Paid (excl. Duties, taxes and VAT)"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:307
msgctxt "age context"
msgid "none"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:308
msgid "Minimum age of 16"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-paket.php:309
msgid "Minimum age of 18"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-finder.php:49
msgid "Shipping \"City\" and \"Postcode\" are empty!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-finder.php:53
#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:194
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:408
#: includes/REST_API/Deutsche_Post/Item_Info.php:258
#: includes/REST_API/Parcel_DE/Item_Info.php:588
#: includes/REST_API/Parcel_DE/Item_Info.php:643
#: includes/REST_API/Parcel_DE/Item_Info.php:689
msgid "Shipping \"Country\" is empty!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-finder.php:73
msgid "No service type selected"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:57
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:127
msgid "DHL Label file is not writable!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:106
msgid "DHL Label file cannot be saved!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:119
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:265
msgid "Please, provide the username in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:123
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:269
msgid "Please, provide the password for the username in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:128
msgid "Please, provide a pickup account in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:132
msgid "Please, provide a distribution center in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:154
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:307
#: includes/REST_API/Deutsche_Post/Item_Info.php:117
#: includes/REST_API/Parcel_DE/Item_Info.php:193
msgid "DHL \"Product\" is empty!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:158
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:339
msgid "Shop \"Order ID\" is empty!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:162
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:343
msgid "Shop \"Weight Units of Measure\" is empty!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:166
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:392
#: includes/REST_API/Deutsche_Post/Item_Info.php:136
#: includes/REST_API/Parcel_DE/Item_Info.php:232
msgid "Order \"Weight\" is empty!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:181
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:400
#: includes/REST_API/Deutsche_Post/Item_Info.php:149
#: includes/REST_API/Parcel_DE/Item_Info.php:243
msgid "Shop \"Currency\" is empty!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:186
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:413
#: includes/REST_API/Deutsche_Post/Item_Info.php:243
#: includes/REST_API/Parcel_DE/Item_Info.php:560
msgid "Shipping \"Address 1\" is empty!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-label.php:190
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:404
#: includes/REST_API/Deutsche_Post/Item_Info.php:249
#: includes/REST_API/Parcel_DE/Item_Info.php:577
#: includes/REST_API/Parcel_DE/Item_Info.php:640
#: includes/REST_API/Parcel_DE/Item_Info.php:686
msgid "Shipping \"City\" is empty!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-paket.php:390
msgid "Your Shipper Address must match a Pickup address on your DHL Portal."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:274
#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel.php:30
msgid "Please, provide an account in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:278
msgid "Please, provide a participation number for the shipping method in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:282
msgid "Please, provide a shipper name in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:286
msgid "Please, provide a shipper address in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:290
msgid "Please, provide a shipper address number in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:294
msgid "Please, provide a shipper city in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:298
msgid "Please, provide a shipper postcode in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:302
msgid "Please, provide a shipper reference in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:314
msgid "Returns are not supported by this DHL Service."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:318
msgid "Please, provide a return name in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:322
msgid "Please, provide a return address in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:326
msgid "Please, provide a return address number in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:330
msgid "Please, provide a return city in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:334
msgid "Please, provide a return postcode in the DHL shipping settings"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:348
msgid "First name and last name must be passed for \"Identity Check\"."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:352
msgid "Either a \"Date of Birth\" or \"Minimum Age\" must be eneted for \"Ident-Check\"."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:370
msgid " A package number is empty. Ensure all package details are filled in."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:374
#: includes/REST_API/Parcel_DE/Item_Info.php:287
msgid "A package weight is empty. Ensure all package details are filled in."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:378
#: includes/REST_API/Parcel_DE/Item_Info.php:296
msgid "A package length is empty. Ensure all package details are filled in."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:382
#: includes/REST_API/Parcel_DE/Item_Info.php:305
msgid "A package width is empty. Ensure all package details are filled in."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:386
#: includes/REST_API/Parcel_DE/Item_Info.php:314
msgid "A package height is empty. Ensure all package details are filled in."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:422
#: includes/REST_API/Parcel_DE/Item_Info.php:626
msgid "Post Number is missing, it is mandatory for \"Packstation\" delivery."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel-de.php:440
#: includes/REST_API/Parcel_DE/Item_Info.php:1270
msgid "Shipping street number is missing!"
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel.php:34
msgid "Please, provide the receiver postnumber."
msgstr ""

#: includes/pr-dhl-api/class-pr-dhl-api-rest-parcel.php:38
msgid "Please, provide the shipment start date."
msgstr ""

#. Translators: %s is the error message from the API.
#. Translators: %s is replaced with the error message returned from the API.
#: includes/REST_API/Deutsche_Post/Client.php:92
#: includes/REST_API/Deutsche_Post/Client.php:126
#: includes/REST_API/Deutsche_Post/Client.php:160
#: includes/REST_API/Deutsche_Post/Client.php:343
#: includes/REST_API/Deutsche_Post/Client.php:371
#: includes/REST_API/Parcel_DE_MyAccount/Client.php:44
#, php-format
msgid "API error: %s"
msgstr ""

#. Translators: %s is the error message from the API.
#: includes/REST_API/Deutsche_Post/Client.php:183
#, php-format
msgid "Failed to get items from the API: %s"
msgstr ""

#. Translators: %s is the error message from the API.
#: includes/REST_API/Deutsche_Post/Client.php:284
#, php-format
msgid "Failed to create order: %s"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:131
msgid "Order \"Service Level\" is invalid"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:139
#: includes/REST_API/Parcel_DE/Item_Info.php:235
msgid "The order \"Weight\" must be a number"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:153
#: includes/REST_API/Parcel_DE/Item_Info.php:247
msgid "Shipment \"Value\" is empty!"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:156
#: includes/REST_API/Parcel_DE/Item_Info.php:250
msgid "The order \"value\" must be a number"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:182
msgid "The importer customs reference \"value\" must be an alphanumeric"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:186
msgid "The importer customs reference \"value\" must be between 1 and 35 characters long"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:198
msgid "The sender customs reference \"value\" must be an alphanumeric"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:202
msgid "The sender customs reference \"value\" must be between 1 and 35 characters long"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:226
msgid "Recipient is empty!"
msgstr ""

#: includes/REST_API/Deutsche_Post/Item_Info.php:287
msgid "Item HS Code must be between 4 and 20 characters long"
msgstr ""

#. Translators: %s is replaced with the error details returned from the API.
#: includes/REST_API/Paket/Client.php:81
#, php-format
msgid "Failed DHL Request Pickup1: %s"
msgstr ""

#. Translators: %s is replaced with the error details returned from the API.
#: includes/REST_API/Paket/Client.php:107
#, php-format
msgid "Failed DHL Request Pickup: %s"
msgstr ""

#: includes/REST_API/Paket/Pickup_Request_Info.php:165
#: includes/REST_API/Parcel_DE/Item_Info.php:214
msgid "Check your settings \"Account Number\" and \"Participation Number\"."
msgstr ""

#: includes/REST_API/Paket/Pickup_Request_Info.php:255
#: includes/REST_API/Paket/Pickup_Request_Info.php:260
#: includes/REST_API/Parcel_DE/Item_Info.php:417
#: includes/REST_API/Parcel_DE/Item_Info.php:422
msgid "Shipper \"Address 1\" is empty!"
msgstr ""

#: includes/REST_API/Paket/Pickup_Request_Info.php:273
#: includes/REST_API/Parcel_DE/Item_Info.php:438
msgid "Shipper \"City\" is empty!"
msgstr ""

#: includes/REST_API/Paket/Pickup_Request_Info.php:277
#: includes/REST_API/Parcel_DE/Item_Info.php:434
msgid "Shipper \"Postcode\" is empty!"
msgstr ""

#: includes/REST_API/Paket/Pickup_Request_Info.php:285
#: includes/REST_API/Parcel_DE/Item_Info.php:454
msgid "Shipper \"Country\" is empty!"
msgstr ""

#. Translators: %s is replaced with the error message returned from the API.
#: includes/REST_API/Parcel_DE/Client.php:51
#: includes/REST_API/Parcel_DE/Client.php:69
#, php-format
msgid "Error creating label: %s"
msgstr ""

#. Translators: %s is replaced with the error message returned from the API.
#: includes/REST_API/Parcel_DE/Client.php:589
#, php-format
msgid "Error deleting label: %s"
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:223
msgid "Could not create account number - no product number."
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:278
msgid "A package number is empty. Ensure all package details are filled in."
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:356
msgid "Master Reference Number (MRN) must be a maximum of 18 alphanumeric characters."
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:375
msgid "Master Reference Number (MRN) is required for shipments valued at €1000 or more."
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:402
msgid "\"Account Name\" in settings is empty."
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:549
msgid "Recipient name is empty!"
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:574
#: includes/REST_API/Parcel_DE/Item_Info.php:637
#: includes/REST_API/Parcel_DE/Item_Info.php:683
msgid "Shipping \"Postcode\" is empty!"
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:619
msgid "Packstation name is empty!"
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:630
#: includes/REST_API/Parcel_DE/Item_Info.php:676
msgid "Locker ID is missing, it is mandatory for \"Packstation\" delivery."
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:665
msgid "Name is empty!"
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:701
msgid "Either post number or email is required for Postfiliale"
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:752
msgid "HS code must be at-least 8 digits when an export declaration is required."
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:756
msgid "HS code must be at-least 6 digits for low-value exports (< €1 000)."
msgstr ""

#: includes/REST_API/Parcel_DE/Item_Info.php:766
msgid "Item HS Code must be between 4 and 11 characters long"
msgstr ""

#. translators: %s is the name of the required argument
#: includes/Utils/Args_Parser.php:58
#, php-format
msgid "Please specify a \"%s\" argument"
msgstr ""

#: pr-dhl-woocommerce.php:159
msgid "Test Connection"
msgstr ""

#: pr-dhl-woocommerce.php:160
msgid "Get Account Settings"
msgstr ""

#: pr-dhl-woocommerce.php:183
msgid "Packstation "
msgstr ""

#: pr-dhl-woocommerce.php:184
#: pr-dhl-woocommerce.php:185
msgid "Postfiliale "
msgstr ""

#: pr-dhl-woocommerce.php:467
msgid "WooCommerce DHL Integration requires WooCommerce to be installed and activated!"
msgstr ""

#: pr-dhl-woocommerce.php:575
msgid "Country not supported"
msgstr ""

#: pr-dhl-woocommerce.php:580
msgid "Connection Successful!"
msgstr ""

#. translators: %s is the error message returned when the connection fails
#: pr-dhl-woocommerce.php:595
#, php-format
msgid "Connection Failed: %s Make sure to save the settings before testing the connection. "
msgstr ""

#: pr-dhl-woocommerce.php:614
msgid "Account Connected"
msgstr ""

#. translators: %s is the error message returned when the connection fails
#: pr-dhl-woocommerce.php:629
#, php-format
msgid "Account Connection Failed: %s . Make sure to save the settings before testing the connection. "
msgstr ""

#: pr-dhl-woocommerce.php:977
msgid "Your DHL account password will expire in less than 30 days, please head to DHL business portal and reset your password."
msgstr ""

#: pr-dhl-woocommerce.php:979
msgid "Your DHL account password will expire in less than 7 days, please head to DHL business portal and reset your password."
msgstr ""

#. translators: %s is the warning message
#: pr-dhl-woocommerce.php:984
#, php-format
msgid "Warning: %s"
msgstr ""

#: pr-dhl-woocommerce.php:1009
msgid "DHL checkout Blocks"
msgstr ""

#: templates/checkout/dhl-closest-drop-point.php:11
msgid "Closest drop-off point"
msgstr ""

#: templates/checkout/dhl-closest-drop-point.php:15
msgid "Preferred delivery to a parcel shop/parcel locker close to the specified home address"
msgstr ""

#: templates/checkout/dhl-closest-drop-point.php:30
msgid "Home delivery"
msgstr ""

#: templates/checkout/dhl-parcel-finder.php:19
msgid "Post Code"
msgstr ""

#: templates/checkout/dhl-parcel-finder.php:28
msgid "Address"
msgstr ""

#: templates/checkout/dhl-parcel-finder.php:45
msgid "Search"
msgstr ""

#: templates/checkout/dhl-preferred-services.php:19
msgid "DHL Preferred Delivery. Delivered just as you wish."
msgstr ""

#: templates/checkout/dhl-preferred-services.php:25
msgid ""
"Thanks to the ﬂexible recipient services of DHL Preferred Delivery, you decide\r\n"
"when and where you want to receive your parcels.<br>\r\n"
"Please choose your preferred delivery option."
msgstr ""

#: templates/checkout/dhl-preferred-services.php:40
msgid "Delivery day: Delivery at your preferred day"
msgstr ""

#: templates/checkout/dhl-preferred-services.php:41
msgid "Choose one of the displayed days as your preferred day for your parcel delivery. Other days are not possible due to delivery processes."
msgstr ""

#. Translators: %s is the surcharge amount including VAT.
#: templates/checkout/dhl-preferred-services.php:50
#, php-format
msgid "There is a surcharge of %s incl. VAT for this service.*"
msgstr ""

#: templates/checkout/dhl-preferred-services.php:90
msgid "Unfortunately, for the selected delivery address the service Delivery Day is not available"
msgstr ""

#: templates/checkout/dhl-preferred-services.php:112
msgid "Drop-off location or neighbor"
msgstr ""

#: templates/checkout/dhl-preferred-services.php:167
msgid "Drop-off location: Delivery to your preferred drop-off location"
msgstr ""

#: templates/checkout/dhl-preferred-services.php:168
msgid "Choose a weather-protected and non-visible place on your property, where we can deposit the parcel in your absence."
msgstr ""

#: templates/checkout/dhl-preferred-services.php:178
msgid "e.g. Garage, Terrace"
msgstr ""

#: templates/checkout/dhl-preferred-services.php:189
msgid "Neighbour: Delivery to a neighbour of your choice"
msgstr ""

#: templates/checkout/dhl-preferred-services.php:189
msgid "Determine a person in your immediate neighborhood whom we can hand out your parcel in your absence. This person should live in the same building, directly opposite or next door."
msgstr ""

#: templates/checkout/dhl-preferred-services.php:197
msgid "First name, last name of neighbour"
msgstr ""

#: templates/checkout/dhl-preferred-services.php:205
msgid "Street, number, postal code, city"
msgstr ""

#: templates/dhl-handover/body.php:19
#: templates/dhl-handover/body.php:129
#: templates/dhl-handover/head.php:12
msgid "Print"
msgstr ""

#: templates/dhl-handover/body.php:24
msgid "Handover Note"
msgstr ""

#: templates/dhl-handover/body.php:34
msgid "Pick-up Account Details"
msgstr ""

#: templates/dhl-handover/body.php:39
msgid "Pick-up Name"
msgstr ""

#: templates/dhl-handover/body.php:44
msgid "Account No"
msgstr ""

#: templates/dhl-handover/body.php:56
#: templates/dhl-handover/body.php:59
msgid "Shipping Service(s)"
msgstr ""

#: templates/dhl-handover/body.php:66
msgid "Details"
msgstr ""

#: templates/dhl-handover/body.php:71
msgid "Total"
msgstr ""

#: templates/dhl-handover/body.php:73
msgid "No. of items"
msgstr ""

#. translators: %s: weight unit (e.g., kg, lbs)
#: templates/dhl-handover/body.php:80
#, php-format
msgid "Weight (%s)"
msgstr ""

#: templates/dhl-handover/body.php:86
msgid "No. of Receptacles"
msgstr ""

#: templates/dhl-handover/body.php:92
msgid "Handover info"
msgstr ""

#: templates/dhl-handover/body.php:95
msgid "Drop-Off"
msgstr ""

#: templates/dhl-handover/body.php:98
msgid "Pick-Up"
msgstr ""

#: templates/dhl-handover/body.php:102
msgid "DHL Distribution centre"
msgstr ""

#: templates/dhl-handover/body.php:108
msgid "Remarks/VAS"
msgstr ""

#: templates/dhl-handover/body.php:117
#: templates/dhl-handover/body.php:122
msgid "Signature"
msgstr ""

#: templates/dhl-handover/body.php:120
msgid "I declare the contents of the shipment under this Handover Note does not contain any prohibited or hazardous goods. The General Terms and Conditions of DHL eCommerce shall apply on the services provided by DHL eCommerce."
msgstr ""

#: templates/dhl-handover/body.php:124
msgid "Date"
msgstr ""

#: templates/dhl-handover/head.php:6
msgid "DHL Handover"
msgstr ""

#: includes/checkout-blocks/dhl-parcel-finder/block.json
#: includes/checkout-blocks/dhl-preferred-services/block.json
msgctxt "block title"
msgid "DHL Order Block"
msgstr ""

#: includes/checkout-blocks/dhl-parcel-finder/block.json
#: includes/checkout-blocks/dhl-preferred-services/block.json
msgctxt "block description"
msgid "Adds DHL block to checkout."
msgstr ""
