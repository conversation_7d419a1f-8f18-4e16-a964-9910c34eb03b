{"name": "dhl-for-woocommerce", "version": "3.9.3", "author": "DHL", "license": "GPL-2.0-or-later", "main": "build/index.js", "scripts": {"build": "npm run prebuild && composer install && wp-scripts build && npm run makepot && composer install --no-dev && npm run archive", "archive": "composer archive --file=dhl-for-woocommerce --format=zip", "prebuild": "rm -rf $npm_package_name && rm -f $npm_package_name.zip", "format": "wp-scripts format", "lint:css": "wp-scripts lint-style", "lint:js": "wp-scripts lint-js", "packages-update": "wp-scripts packages-update", "start": "wp-scripts start", "env": "wp-env", "makepot": "vendor/bin/wp i18n make-pot ./ lang/$npm_package_name.pot --exclude=node_modules,tests,docs,vendor"}, "dependencies": {"@wordpress/icons": "^9.49.0", "lodash": "^4.17.21"}, "devDependencies": {"@woocommerce/dependency-extraction-webpack-plugin": "^2.2.0", "@woocommerce/eslint-plugin": "^2.2.0", "@wordpress/env": "^7.0.0", "@wordpress/prettier-config": "^2.25.13", "@wordpress/scripts": "^30.15.0", "prettier": "npm:wp-prettier@^2.6.2"}}