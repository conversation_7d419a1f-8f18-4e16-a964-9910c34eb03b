# DHL WooCommerce Plugin Modernization Guide

## Overview

This document outlines the modernization approach for the DHL WooCommerce plugin, focusing on **backward compatibility**, **WordPress/WooCommerce standards compliance**, and **production safety** for 8000+ active installations.

## Core Principles

### 1. Backward Compatibility First
- **Never break existing APIs** - All public methods must remain functional
- **Maintain existing hooks and filters** - Third-party integrations must continue working
- **Preserve database schema** - No breaking changes to stored data
- **Keep existing settings accessible** - All configuration options remain available

### 2. WordPress/WooCommerce Standards
- Follow [WordPress Coding Standards](https://developer.wordpress.com/docs/coding-standards/)
- Use WordPress naming conventions (`class-`, `function_`, etc.)
- Proper sanitization, validation, and escaping
- WordPress-style documentation blocks
- WooCommerce compatibility declarations

### 3. Incremental Improvements
- Small, testable changes
- Feature flags for new functionality
- Gradual deprecation with proper notices
- Extensive testing at each step

## Architecture Improvements

### Service Container Implementation

**File**: `includes/class-pr-dhl-service-container.php`

A lightweight dependency injection container that:
- Follows WordPress coding standards
- Maintains singleton pattern compatibility
- Provides service binding and resolution
- Includes backward-compatible helper functions

```php
// New way (optional)
$settings = pr_dhl_resolve('settings');

// Old way (still works)
$settings = PR_DHL()->get_shipping_dhl_settings();
```

### Settings Service Enhancement

**File**: `includes/class-pr-dhl-settings-service.php`

Improved settings management with:
- Environment variable support for credentials
- Better validation and sanitization
- Settings caching for performance
- Backward-compatible interface

```php
// Enhanced settings access
$settings_service = PR_DHL()->get_settings_service();
$paket_settings = $settings_service->get_settings('paket');

// Environment variable override
putenv('DHL_PAKET_USERNAME=your_username');
// Automatically used when available
```

## Testing Strategy

### Comprehensive Test Suite

**Location**: `tests/`

- **Backward Compatibility Tests** - Ensure existing functionality works
- **Integration Tests** - Verify new services work with existing code
- **Settings Tests** - Validate configuration management
- **WordPress Standards Tests** - Code quality and compliance

### Running Tests

```bash
# Install dependencies
composer install

# Run PHPUnit tests
vendor/bin/phpunit

# Run coding standards check
vendor/bin/phpcs

# Check PHP compatibility
vendor/bin/phpcs --standard=PHPCompatibility --runtime-set testVersion 7.4-
```

### Continuous Integration

**File**: `.github/workflows/test.yml`

Automated testing across:
- PHP versions: 7.4, 8.0, 8.1, 8.2
- WordPress versions: 6.6, 6.7, 6.8
- WooCommerce versions: 9.8, 10.0

## Implementation Phases

### Phase 1: Foundation (COMPLETED)
✅ Service Container Implementation  
✅ Settings Service Enhancement  
✅ Comprehensive Test Suite  
✅ CI/CD Pipeline Setup  

### Phase 2: Gradual Migration (IN PROGRESS)
- [ ] Migrate order handling to service layer
- [ ] Enhance API client with better error handling
- [ ] Implement caching for API responses
- [ ] Add structured logging

### Phase 3: Performance & Quality (PLANNED)
- [ ] Database query optimization
- [ ] Frontend asset optimization
- [ ] Security audit and improvements
- [ ] Documentation updates

## Usage Examples

### For Developers

#### Using the New Service Container

```php
// Get the container
$container = pr_dhl_container();

// Register a custom service
$container->bind('my_service', function() {
    return new My_Custom_Service();
});

// Resolve services
$logger = pr_dhl_resolve('logger');
$settings = pr_dhl_resolve('settings');
```

#### Enhanced Settings Management

```php
// Get settings service
$settings_service = PR_DHL()->get_settings_service();

// Get specific service settings
$paket_settings = $settings_service->get_settings('paket');

// Get individual setting with default
$api_user = $settings_service->get_setting('paket', 'api_user', '');

// Update settings
$settings_service->update_settings('paket', array(
    'enabled' => 'yes',
    'title' => 'DHL Paket Shipping'
));
```

### For Site Administrators

#### Environment Variable Configuration

For enhanced security, you can now configure API credentials via environment variables:

```bash
# In your .env file or server environment
DHL_PAKET_USERNAME=your_api_username
DHL_PAKET_PASSWORD=your_api_password
DHL_PAKET_ACCOUNT=your_account_number
DHL_PAKET_SANDBOX=yes
```

These will automatically override database settings when available.

## Migration Guide

### For Existing Installations

1. **No immediate action required** - All existing functionality continues to work
2. **Optional**: Set environment variables for enhanced security
3. **Recommended**: Update to latest version for bug fixes and improvements

### For Developers Extending the Plugin

1. **Existing hooks and filters** continue to work as before
2. **New service container** available for dependency injection
3. **Enhanced settings API** provides better validation and caching
4. **Backward compatibility** maintained for all public APIs

## Security Improvements

### Environment Variable Support
- API credentials can be stored in environment variables
- Automatic fallback to database settings
- Prevents credential exposure in backups

### Enhanced Validation
- All settings are validated and sanitized
- Type checking for configuration values
- Protection against XSS and injection attacks

### Secure Configuration
- Sensitive data handling improvements
- Better nonce verification
- Enhanced capability checks

## Performance Enhancements

### Settings Caching
- Automatic caching of frequently accessed settings
- Cache invalidation on updates
- Reduced database queries

### Service Container
- Singleton pattern for shared services
- Lazy loading of dependencies
- Memory usage optimization

## Troubleshooting

### Common Issues

1. **Settings not loading**: Clear cache with `$settings_service->clear_settings_cache()`
2. **Service not found**: Ensure service is properly registered in container
3. **Environment variables not working**: Check server configuration

### Debug Mode

Enable debug logging by setting:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Contributing

### Code Standards
- Follow WordPress Coding Standards
- Include PHPDoc comments
- Write tests for new functionality
- Maintain backward compatibility

### Testing Requirements
- All new code must have tests
- Existing tests must pass
- Code coverage should not decrease
- Manual testing on multiple environments

## Support

For questions or issues:
1. Check existing GitHub issues
2. Review this documentation
3. Run the test suite to verify functionality
4. Create detailed bug reports with environment information

---

**Remember**: This modernization maintains full backward compatibility. Existing sites will continue to work without any changes required.
