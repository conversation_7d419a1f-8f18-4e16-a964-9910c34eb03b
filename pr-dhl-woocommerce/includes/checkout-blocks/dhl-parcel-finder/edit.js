/**
 * External dependencies
 */
import {__} from '@wordpress/i18n';
import {
    useBlockProps,
    RichText,
    InspectorControls,
} from '@wordpress/block-editor';
import {PanelBody, SelectControl, RadioControl, TextControl, Disabled} from '@wordpress/components';
import {getSetting} from '@woocommerce/settings';

/**
 * Internal dependencies
 */
import './style.scss';


export const Edit = ({attributes, setAttributes}) => {
    return (null);

};

export const Save = ({attributes}) => {
    return (null);
};