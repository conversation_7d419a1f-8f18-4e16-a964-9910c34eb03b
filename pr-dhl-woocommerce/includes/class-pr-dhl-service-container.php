<?php
/**
 * DHL Service Container
 *
 * A lightweight dependency injection container that follows WordPress coding standards
 * and maintains backward compatibility.
 *
 * @package PR_DHL_WC
 * @since   3.10.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class PR_DHL_Service_Container
 *
 * Provides a simple service container for managing dependencies while maintaining
 * full backward compatibility with existing code.
 */
class PR_DHL_Service_Container {

	/**
	 * Container instance
	 *
	 * @var PR_DHL_Service_Container
	 */
	private static $instance = null;

	/**
	 * Service bindings
	 *
	 * @var array
	 */
	private $bindings = array();

	/**
	 * Singleton instances
	 *
	 * @var array
	 */
	private $instances = array();

	/**
	 * Get container instance
	 *
	 * @return PR_DHL_Service_Container
	 */
	public static function instance() {
		if ( is_null( self::$instance ) ) {
			self::$instance = new self();
		}

		return self::$instance;
	}

	/**
	 * Private constructor to enforce singleton
	 */
	private function __construct() {
		$this->register_core_services();
	}

	/**
	 * Register core services
	 *
	 * @since 3.10.0
	 */
	private function register_core_services() {
		// Register logger service
		$this->bind( 'logger', array( $this, 'create_logger' ) );

		// Register API factory service
		$this->bind( 'api_factory', array( $this, 'create_api_factory' ) );

		// Register settings service
		$this->bind( 'settings', array( $this, 'create_settings_service' ) );
	}

	/**
	 * Bind a service to the container
	 *
	 * @param string   $abstract Service identifier.
	 * @param callable $concrete Service factory function.
	 * @param bool     $singleton Whether to treat as singleton.
	 *
	 * @since 3.10.0
	 */
	public function bind( $abstract, $concrete, $singleton = false ) {
		$this->bindings[ $abstract ] = array(
			'concrete'  => $concrete,
			'singleton' => $singleton,
		);
	}

	/**
	 * Bind a singleton service
	 *
	 * @param string   $abstract Service identifier.
	 * @param callable $concrete Service factory function.
	 *
	 * @since 3.10.0
	 */
	public function singleton( $abstract, $concrete ) {
		$this->bind( $abstract, $concrete, true );
	}

	/**
	 * Resolve a service from the container
	 *
	 * @param string $abstract Service identifier.
	 *
	 * @return mixed
	 * @throws Exception If service not found.
	 *
	 * @since 3.10.0
	 */
	public function resolve( $abstract ) {
		if ( ! isset( $this->bindings[ $abstract ] ) ) {
			throw new Exception( sprintf( 'Service "%s" not found in container', $abstract ) );
		}

		$binding = $this->bindings[ $abstract ];

		// Return singleton instance if exists
		if ( $binding['singleton'] && isset( $this->instances[ $abstract ] ) ) {
			return $this->instances[ $abstract ];
		}

		// Create new instance
		$instance = call_user_func( $binding['concrete'] );

		// Store singleton instance
		if ( $binding['singleton'] ) {
			$this->instances[ $abstract ] = $instance;
		}

		return $instance;
	}

	/**
	 * Check if service is bound
	 *
	 * @param string $abstract Service identifier.
	 *
	 * @return bool
	 * @since 3.10.0
	 */
	public function has( $abstract ) {
		return isset( $this->bindings[ $abstract ] );
	}

	/**
	 * Create logger service
	 *
	 * @return PR_DHL_Logger
	 * @since 3.10.0
	 */
	private function create_logger() {
		if ( ! class_exists( 'PR_DHL_Logger' ) ) {
			require_once PR_DHL_PLUGIN_DIR_PATH . '/includes/class-pr-dhl-logger.php';
		}

		return new PR_DHL_Logger();
	}

	/**
	 * Create API factory service
	 *
	 * @return PR_DHL_API_Factory
	 * @since 3.10.0
	 */
	private function create_api_factory() {
		if ( ! class_exists( 'PR_DHL_API_Factory' ) ) {
			require_once PR_DHL_PLUGIN_DIR_PATH . '/includes/pr-dhl-api/class-pr-dhl-api-factory.php';
		}

		return new PR_DHL_API_Factory();
	}

	/**
	 * Create settings service
	 *
	 * @return PR_DHL_Settings_Service
	 * @since 3.10.0
	 */
	private function create_settings_service() {
		if ( ! class_exists( 'PR_DHL_Settings_Service' ) ) {
			require_once PR_DHL_PLUGIN_DIR_PATH . '/includes/class-pr-dhl-settings-service.php';
		}

		return new PR_DHL_Settings_Service();
	}
}

/**
 * Get the service container instance
 *
 * @return PR_DHL_Service_Container
 * @since 3.10.0
 */
function pr_dhl_container() {
	return PR_DHL_Service_Container::instance();
}

/**
 * Resolve a service from the container
 *
 * @param string $service Service identifier.
 *
 * @return mixed
 * @since 3.10.0
 */
function pr_dhl_resolve( $service ) {
	return pr_dhl_container()->resolve( $service );
}
