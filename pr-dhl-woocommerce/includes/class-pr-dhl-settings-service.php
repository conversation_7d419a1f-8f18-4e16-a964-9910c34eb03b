<?php
/**
 * DHL Settings Service
 *
 * Provides improved settings management while maintaining backward compatibility
 * with existing settings access patterns.
 *
 * @package PR_DHL_WC
 * @since   3.10.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class PR_DHL_Settings_Service
 *
 * Manages plugin settings with improved validation, caching, and security
 * while maintaining full backward compatibility.
 */
class PR_DHL_Settings_Service {

	/**
	 * Settings cache
	 *
	 * @var array
	 */
	private $settings_cache = array();

	/**
	 * Default settings
	 *
	 * @var array
	 */
	private $defaults = array();

	/**
	 * Constructor
	 *
	 * @since 3.10.0
	 */
	public function __construct() {
		$this->init_defaults();
		$this->init_hooks();
	}

	/**
	 * Initialize default settings
	 *
	 * @since 3.10.0
	 */
	private function init_defaults() {
		$this->defaults = array(
			'paket' => array(
				'enabled'                    => 'no',
				'title'                      => __( 'DHL Paket', 'dhl-for-woocommerce' ),
				'sandbox'                    => 'no',
				'api_user'                   => '',
				'api_pwd'                    => '',
				'account_num'                => '',
				'participation_v2'           => '',
				'default_product_int'        => 'V01PAK',
				'default_product_dom'        => 'V01PAK',
				'label_format'               => 'PDF',
				'label_size'                 => 'A4',
				'tracking_note'              => 'yes',
				'tracking_note_txt'          => __( 'Your order has been shipped with DHL. Tracking number: {tracking_number}', 'dhl-for-woocommerce' ),
			),
			'deutsche_post' => array(
				'enabled'                    => 'no',
				'title'                      => __( 'Deutsche Post', 'dhl-for-woocommerce' ),
				'api_user'                   => '',
				'api_pwd'                    => '',
				'participation_v2'           => '',
				'default_product_int'        => 'V53WPAK',
				'label_format'               => 'PDF',
				'tracking_note'              => 'yes',
			),
		);

		/**
		 * Filter default DHL settings
		 *
		 * @param array $defaults Default settings array.
		 *
		 * @since 3.10.0
		 */
		$this->defaults = apply_filters( 'pr_dhl_default_settings', $this->defaults );
	}

	/**
	 * Initialize hooks
	 *
	 * @since 3.10.0
	 */
	private function init_hooks() {
		// Clear cache when settings are updated
		add_action( 'update_option_woocommerce_pr_dhl_paket_settings', array( $this, 'clear_settings_cache' ) );
		add_action( 'update_option_woocommerce_pr_dhl_deutsche_post_settings', array( $this, 'clear_settings_cache' ) );
	}

	/**
	 * Get settings for a specific service
	 *
	 * @param string $service Service name (paket, deutsche_post).
	 * @param bool   $force_refresh Force refresh from database.
	 *
	 * @return array
	 * @since 3.10.0
	 */
	public function get_settings( $service, $force_refresh = false ) {
		// Validate service
		if ( ! in_array( $service, array( 'paket', 'deutsche_post' ), true ) ) {
			return array();
		}

		$cache_key = "settings_{$service}";

		// Return cached settings if available
		if ( ! $force_refresh && isset( $this->settings_cache[ $cache_key ] ) ) {
			return $this->settings_cache[ $cache_key ];
		}

		// Get settings from database
		$option_name = "woocommerce_pr_dhl_{$service}_settings";
		$settings    = get_option( $option_name, array() );

		// Merge with defaults
		$settings = wp_parse_args( $settings, $this->defaults[ $service ] );

		// Apply environment variable overrides for security
		$settings = $this->apply_env_overrides( $service, $settings );

		// Validate and sanitize settings
		$settings = $this->validate_settings( $service, $settings );

		// Cache the settings
		$this->settings_cache[ $cache_key ] = $settings;

		return $settings;
	}

	/**
	 * Get a specific setting value
	 *
	 * @param string $service Service name.
	 * @param string $key     Setting key.
	 * @param mixed  $default Default value.
	 *
	 * @return mixed
	 * @since 3.10.0
	 */
	public function get_setting( $service, $key, $default = null ) {
		$settings = $this->get_settings( $service );

		return isset( $settings[ $key ] ) ? $settings[ $key ] : $default;
	}

	/**
	 * Update settings for a service
	 *
	 * @param string $service  Service name.
	 * @param array  $settings Settings array.
	 *
	 * @return bool
	 * @since 3.10.0
	 */
	public function update_settings( $service, $settings ) {
		if ( ! in_array( $service, array( 'paket', 'deutsche_post' ), true ) ) {
			return false;
		}

		// Validate settings before saving
		$settings = $this->validate_settings( $service, $settings );

		$option_name = "woocommerce_pr_dhl_{$service}_settings";
		$result      = update_option( $option_name, $settings );

		// Clear cache
		$this->clear_settings_cache();

		return $result;
	}

	/**
	 * Apply environment variable overrides
	 *
	 * @param string $service  Service name.
	 * @param array  $settings Settings array.
	 *
	 * @return array
	 * @since 3.10.0
	 */
	private function apply_env_overrides( $service, $settings ) {
		$env_mappings = array(
			'paket' => array(
				'api_user'    => 'DHL_PAKET_USERNAME',
				'api_pwd'     => 'DHL_PAKET_PASSWORD',
				'account_num' => 'DHL_PAKET_ACCOUNT',
				'sandbox'     => 'DHL_PAKET_SANDBOX',
			),
			'deutsche_post' => array(
				'api_user' => 'DHL_DP_USERNAME',
				'api_pwd'  => 'DHL_DP_PASSWORD',
				'sandbox'  => 'DHL_DP_SANDBOX',
			),
		);

		if ( isset( $env_mappings[ $service ] ) ) {
			foreach ( $env_mappings[ $service ] as $setting_key => $env_var ) {
				$env_value = getenv( $env_var );
				if ( false !== $env_value ) {
					$settings[ $setting_key ] = $env_value;
				}
			}
		}

		return $settings;
	}

	/**
	 * Validate and sanitize settings
	 *
	 * @param string $service  Service name.
	 * @param array  $settings Settings array.
	 *
	 * @return array
	 * @since 3.10.0
	 */
	private function validate_settings( $service, $settings ) {
		// Sanitize text fields
		$text_fields = array( 'title', 'api_user', 'account_num', 'participation_v2', 'tracking_note_txt' );
		foreach ( $text_fields as $field ) {
			if ( isset( $settings[ $field ] ) ) {
				$settings[ $field ] = sanitize_text_field( $settings[ $field ] );
			}
		}

		// Validate yes/no fields
		$yes_no_fields = array( 'enabled', 'sandbox', 'tracking_note' );
		foreach ( $yes_no_fields as $field ) {
			if ( isset( $settings[ $field ] ) ) {
				$settings[ $field ] = in_array( $settings[ $field ], array( 'yes', 'no' ), true ) ? $settings[ $field ] : 'no';
			}
		}

		// Validate select fields
		if ( 'paket' === $service ) {
			$valid_products = array( 'V01PAK', 'V62WP', 'V66WPI' );
			if ( isset( $settings['default_product_dom'] ) && ! in_array( $settings['default_product_dom'], $valid_products, true ) ) {
				$settings['default_product_dom'] = 'V01PAK';
			}
		}

		return $settings;
	}

	/**
	 * Clear settings cache
	 *
	 * @since 3.10.0
	 */
	public function clear_settings_cache() {
		$this->settings_cache = array();
	}

	/**
	 * Backward compatibility method - get shipping DHL settings
	 *
	 * @return array
	 * @since 3.10.0
	 */
	public function get_shipping_dhl_settings() {
		$base_country = PR_DHL()->get_base_country();

		if ( 'DE' === $base_country ) {
			return $this->get_settings( 'paket' );
		}

		return $this->get_settings( 'deutsche_post' );
	}
}
