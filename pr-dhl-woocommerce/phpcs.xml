<?xml version="1.0"?>
<ruleset name="DHL WooCommerce Plugin">
	<description>WordPress Coding Standards for DHL WooCommerce Plugin</description>

	<!-- What to scan -->
	<file>.</file>
	
	<!-- Exclude patterns -->
	<exclude-pattern>/vendor/</exclude-pattern>
	<exclude-pattern>/node_modules/</exclude-pattern>
	<exclude-pattern>/tests/</exclude-pattern>
	<exclude-pattern>/build/</exclude-pattern>
	<exclude-pattern>/assets/js/jquery*</exclude-pattern>
	<exclude-pattern>*.min.js</exclude-pattern>
	<exclude-pattern>*.min.css</exclude-pattern>

	<!-- How to scan -->
	<arg value="sp"/> <!-- Show sniff and progress -->
	<arg name="basepath" value="./"/><!-- Strip the file paths down to the relevant bit -->
	<arg name="colors"/>
	<arg name="extensions" value="php"/>
	<arg name="parallel" value="8"/><!-- Enables parallel processing when available for faster results -->

	<!-- Rules: Check PHP version compatibility -->
	<config name="testVersion" value="7.4-"/>
	<rule ref="PHPCompatibilityWP"/>

	<!-- Rules: WordPress Coding Standards -->
	<rule ref="WordPress-Core">
		<!-- Allow short array syntax -->
		<exclude name="Generic.Arrays.DisallowShortArraySyntax"/>
		
		<!-- Allow multiple assignments in one line for readability -->
		<exclude name="Squiz.PHP.DisallowMultipleAssignments"/>
	</rule>
	
	<rule ref="WordPress-Docs">
		<!-- Don't require documentation for every function -->
		<exclude name="Squiz.Commenting.FunctionComment.Missing"/>
		<exclude name="Squiz.Commenting.ClassComment.Missing"/>
		<exclude name="Squiz.Commenting.VariableComment.Missing"/>
	</rule>

	<rule ref="WordPress-Extra">
		<!-- Allow short ternary operator -->
		<exclude name="WordPress.PHP.DisallowShortTernary"/>
	</rule>

	<!-- Rules: WooCommerce Coding Standards -->
	<rule ref="WooCommerce-Core"/>

	<!-- Enforce PSR-4 autoloading standard for new classes -->
	<rule ref="PSR1.Classes.ClassDeclaration"/>

	<!-- Check for PHP cross-version compatibility -->
	<rule ref="PHPCompatibility">
		<!-- Exclude PHP 8+ features that we don't use yet -->
		<exclude name="PHPCompatibility.FunctionUse.NewFunctions.str_containsFound"/>
	</rule>

	<!-- Customizations for specific files -->
	<rule ref="WordPress.Files.FileName">
		<properties>
			<!-- Allow class files to use WordPress naming convention -->
			<property name="strict_class_file_names" value="false"/>
		</properties>
	</rule>

	<!-- Allow longer lines for readability in some cases -->
	<rule ref="Generic.Files.LineLength">
		<properties>
			<property name="lineLimit" value="120"/>
			<property name="absoluteLineLimit" value="150"/>
		</properties>
	</rule>

	<!-- Customizations for WordPress hooks -->
	<rule ref="WordPress.NamingConventions.ValidHookName">
		<properties>
			<property name="additionalWordDelimiters" value="-"/>
		</properties>
	</rule>

	<!-- Allow camelCase for modern PHP classes -->
	<rule ref="WordPress.NamingConventions.ValidVariableName">
		<properties>
			<property name="customPropertiesWhitelist" type="array">
				<element value="apiClient"/>
				<element value="baseUrl"/>
				<element value="restApi"/>
			</property>
		</properties>
	</rule>

	<!-- Text Domain verification -->
	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array">
				<element value="dhl-for-woocommerce"/>
			</property>
		</properties>
	</rule>

	<!-- Exclude specific rules for legacy code -->
	<rule ref="WordPress.Security.EscapeOutput">
		<!-- Allow unescaped output in specific admin contexts -->
		<exclude-pattern>*/includes/class-pr-dhl-wc-wizard-*.php</exclude-pattern>
	</rule>

	<!-- Database queries -->
	<rule ref="WordPress.DB.DirectDatabaseQuery">
		<!-- Allow direct queries in specific migration/setup contexts -->
		<exclude-pattern>*/includes/class-pr-dhl-*-migration.php</exclude-pattern>
	</rule>

	<!-- Nonce verification -->
	<rule ref="WordPress.Security.NonceVerification">
		<!-- Exclude AJAX handlers that have their own nonce verification -->
		<exclude-pattern>*/includes/class-pr-dhl-wc-order-*.php</exclude-pattern>
	</rule>
</ruleset>
