<?php
/**
 * Test that existing functionality remains unchanged
 *
 * @package DHL_For_WooCommerce
 */

class Test_Existing_Functionality extends DHL_Unit_Test_Case {

	/**
	 * Test that hardcoded API credentials still work
	 */
	public function test_hardcoded_credentials_unchanged() {
		// Test that hardcoded constants are still defined
		$this->assertTrue( defined( 'PR_DHL_CIG_USR' ) );
		$this->assertTrue( defined( 'PR_DHL_CIG_PWD' ) );
		$this->assertTrue( defined( 'PR_DHL_CIG_AUTH' ) );
		$this->assertTrue( defined( 'PR_DHL_CIG_AUTH_QA' ) );
		
		// Test that values are still the expected hardcoded values
		$this->assertEquals( 'dhl_woocommerce_plugin_2_2', PR_DHL_CIG_USR );
		$this->assertEquals( 'egOcb8buCPuqxFDf9fyOdWz6z7pKAQ', PR_DHL_CIG_PWD );
		$this->assertEquals( 'https://cig.dhl.de/services/production/rest', PR_DHL_CIG_AUTH );
		$this->assertEquals( 'https://cig.dhl.de/services/sandbox/rest', PR_DHL_CIG_AUTH_QA );
	}

	/**
	 * Test that sandbox functionality works as before
	 */
	public function test_sandbox_functionality_unchanged() {
		// Set sandbox mode
		update_option( 'woocommerce_pr_dhl_paket_settings', array(
			'enabled' => 'yes',
			'sandbox' => 'yes',
		) );
		
		$plugin = PR_DHL();
		$settings = $plugin->get_shipping_dhl_settings();
		
		// Should still have sandbox setting
		$this->assertEquals( 'yes', $settings['sandbox'] );
	}

	/**
	 * Test that existing API URL method still works
	 */
	public function test_api_url_method_unchanged() {
		$plugin = PR_DHL();
		
		// This method should still exist and work
		$this->assertTrue( method_exists( $plugin, 'get_api_url' ) );
		
		// Should return the expected URL structure
		try {
			$api_url = $plugin->get_api_url();
			$this->assertIsString( $api_url );
			$this->assertStringContainsString( 'dhl.de', $api_url );
		} catch ( Exception $e ) {
			// Expected if no proper DHL settings configured, but method should exist
			$this->assertInstanceOf( 'Exception', $e );
		}
	}

	/**
	 * Test that existing DHL factory still works
	 */
	public function test_dhl_factory_unchanged() {
		$plugin = PR_DHL();
		
		// Factory method should still exist
		$this->assertTrue( method_exists( $plugin, 'get_dhl_factory' ) );
		
		// Should return DHL object
		$dhl_obj = $plugin->get_dhl_factory();
		$this->assertNotNull( $dhl_obj );
		$this->assertTrue( $dhl_obj->is_dhl_paket() );
	}

	/**
	 * Test that existing settings method returns same structure
	 */
	public function test_settings_structure_unchanged() {
		$plugin = PR_DHL();
		$settings = $plugin->get_shipping_dhl_settings();
		
		// Should still be an array
		$this->assertIsArray( $settings );
		
		// Should have expected keys (when settings exist)
		if ( ! empty( $settings ) ) {
			$expected_keys = array( 'enabled', 'title' );
			foreach ( $expected_keys as $key ) {
				$this->assertArrayHasKey( $key, $settings );
			}
		}
	}

	/**
	 * Test that existing constants are unchanged
	 */
	public function test_existing_constants_unchanged() {
		$expected_constants = array(
			'PR_DHL_GLOBAL_URL' => 'https://api.dhl.com',
			'PR_DHL_GLOBAL_API' => 'l7do9bl8gS6y9aHys0u3NR5uqAufPARS',
			'PR_DHL_GLOBAL_SECRET' => '3128XM6J5XHt6knH',
			'PR_DHL_PAKET_TRACKING_URL' => 'https://www.dhl.de/de/privatkunden/dhl-sendungsverfolgung.html?piececode=',
		);

		foreach ( $expected_constants as $constant => $expected_value ) {
			$this->assertTrue( defined( $constant ), "Constant {$constant} should be defined" );
			$this->assertEquals( $expected_value, constant( $constant ), "Constant {$constant} should have unchanged value" );
		}
	}

	/**
	 * Test that existing shipping method registration works
	 */
	public function test_shipping_method_registration_unchanged() {
		// Trigger shipping methods loading
		do_action( 'woocommerce_shipping_init' );
		
		$shipping_methods = WC()->shipping()->get_shipping_methods();
		
		// DHL Paket method should still be registered
		$this->assertArrayHasKey( 'pr_dhl_paket', $shipping_methods );
		$this->assertInstanceOf( 'PR_DHL_WC_Method_Paket', $shipping_methods['pr_dhl_paket'] );
	}

	/**
	 * Test that existing AJAX handlers are unchanged
	 */
	public function test_ajax_handlers_unchanged() {
		$plugin = PR_DHL();
		
		// Test connection callback should still exist
		$this->assertTrue( method_exists( $plugin, 'test_dhl_connection_callback' ) );
		$this->assertTrue( method_exists( $plugin, 'dhl_get_myaccount_callback' ) );
		
		// AJAX actions should still be registered
		$this->assertGreaterThan( 0, has_action( 'wp_ajax_test_dhl_connection', array( $plugin, 'test_dhl_connection_callback' ) ) );
		$this->assertGreaterThan( 0, has_action( 'wp_ajax_dhl_get_myaccount', array( $plugin, 'dhl_get_myaccount_callback' ) ) );
	}

	/**
	 * Test that existing logging functionality works
	 */
	public function test_logging_functionality_unchanged() {
		$plugin = PR_DHL();
		
		// Logging methods should still exist
		$this->assertTrue( method_exists( $plugin, 'log_msg' ) );
		$this->assertTrue( method_exists( $plugin, 'get_log_url' ) );
		
		// Should be able to log messages (basic test)
		try {
			$plugin->log_msg( 'Test message' );
			$this->assertTrue( true ); // If no exception, logging works
		} catch ( Exception $e ) {
			// Expected if logger not properly initialized, but method should exist
			$this->assertInstanceOf( 'Exception', $e );
		}
	}

	/**
	 * Test that existing order and product classes still work
	 */
	public function test_order_product_classes_unchanged() {
		$plugin = PR_DHL();
		
		// Methods should still exist
		$this->assertTrue( method_exists( $plugin, 'get_pr_dhl_wc_order' ) );
		$this->assertTrue( method_exists( $plugin, 'get_pr_dhl_wc_product' ) );
		
		// Should return proper objects
		$order_handler = $plugin->get_pr_dhl_wc_order();
		$product_handler = $plugin->get_pr_dhl_wc_product();
		
		if ( $order_handler ) {
			$this->assertInstanceOf( 'PR_DHL_WC_Order_Paket', $order_handler );
		}
		
		if ( $product_handler ) {
			$this->assertInstanceOf( 'PR_DHL_WC_Product_Paket', $product_handler );
		}
	}

	/**
	 * Test that no environment variables are required
	 */
	public function test_no_env_vars_required() {
		// Clear any environment variables that might exist
		putenv( 'DHL_PAKET_USERNAME' );
		putenv( 'DHL_PAKET_PASSWORD' );
		putenv( 'DHL_PAKET_SANDBOX' );
		
		// Plugin should still work without environment variables
		$plugin = PR_DHL();
		$settings = $plugin->get_shipping_dhl_settings();
		
		// Should return array (even if empty)
		$this->assertIsArray( $settings );
		
		// Should not throw exceptions
		$this->assertTrue( true );
	}
}
