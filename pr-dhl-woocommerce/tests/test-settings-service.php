<?php
/**
 * Test DHL Settings Service
 *
 * @package DHL_For_WooCommerce
 */

class Test_Settings_Service extends DHL_Unit_Test_Case {

	/**
	 * Settings service instance
	 *
	 * @var PR_DHL_Settings_Service
	 */
	private $settings_service;

	/**
	 * Setup test case
	 */
	public function setUp(): void {
		parent::setUp();
		
		// Load the settings service
		require_once PR_DHL_PLUGIN_DIR_PATH . '/includes/class-pr-dhl-settings-service.php';
		$this->settings_service = new PR_DHL_Settings_Service();
	}

	/**
	 * Test getting default settings
	 */
	public function test_get_default_settings() {
		// Clear any existing settings
		delete_option( 'woocommerce_pr_dhl_paket_settings' );
		
		$settings = $this->settings_service->get_settings( 'paket', true );
		
		$this->assertIsArray( $settings );
		$this->assertEquals( 'no', $settings['enabled'] );
		$this->assertEquals( 'DHL Paket', $settings['title'] );
		$this->assertEquals( 'no', $settings['sandbox'] );
		$this->assertEquals( 'V01PAK', $settings['default_product_dom'] );
	}

	/**
	 * Test getting specific setting
	 */
	public function test_get_specific_setting() {
		$title = $this->settings_service->get_setting( 'paket', 'title' );
		$this->assertEquals( 'DHL Paket', $title );
		
		$nonexistent = $this->settings_service->get_setting( 'paket', 'nonexistent', 'default_value' );
		$this->assertEquals( 'default_value', $nonexistent );
	}

	/**
	 * Test updating settings
	 */
	public function test_update_settings() {
		$new_settings = array(
			'enabled' => 'yes',
			'title'   => 'Custom DHL Title',
			'sandbox' => 'yes',
		);
		
		$result = $this->settings_service->update_settings( 'paket', $new_settings );
		$this->assertTrue( $result );
		
		// Verify settings were saved
		$saved_settings = $this->settings_service->get_settings( 'paket', true );
		$this->assertEquals( 'yes', $saved_settings['enabled'] );
		$this->assertEquals( 'Custom DHL Title', $saved_settings['title'] );
		$this->assertEquals( 'yes', $saved_settings['sandbox'] );
	}

	/**
	 * Test settings validation
	 */
	public function test_settings_validation() {
		$invalid_settings = array(
			'enabled'             => 'invalid_value',
			'title'               => '<script>alert("xss")</script>',
			'sandbox'             => 'maybe',
			'default_product_dom' => 'INVALID_PRODUCT',
		);
		
		$this->settings_service->update_settings( 'paket', $invalid_settings );
		$settings = $this->settings_service->get_settings( 'paket', true );
		
		// Should be sanitized to 'no' for invalid yes/no values
		$this->assertEquals( 'no', $settings['enabled'] );
		$this->assertEquals( 'no', $settings['sandbox'] );
		
		// Should be sanitized
		$this->assertStringNotContainsString( '<script>', $settings['title'] );
		
		// Should fallback to default for invalid product
		$this->assertEquals( 'V01PAK', $settings['default_product_dom'] );
	}

	/**
	 * Test environment variable overrides
	 */
	public function test_environment_overrides() {
		// Set environment variables
		putenv( 'DHL_PAKET_USERNAME=env_username' );
		putenv( 'DHL_PAKET_PASSWORD=env_password' );
		putenv( 'DHL_PAKET_SANDBOX=yes' );
		
		$settings = $this->settings_service->get_settings( 'paket', true );
		
		$this->assertEquals( 'env_username', $settings['api_user'] );
		$this->assertEquals( 'env_password', $settings['api_pwd'] );
		$this->assertEquals( 'yes', $settings['sandbox'] );
		
		// Clean up
		putenv( 'DHL_PAKET_USERNAME' );
		putenv( 'DHL_PAKET_PASSWORD' );
		putenv( 'DHL_PAKET_SANDBOX' );
	}

	/**
	 * Test settings caching
	 */
	public function test_settings_caching() {
		// First call should hit database
		$settings1 = $this->settings_service->get_settings( 'paket' );
		
		// Second call should use cache
		$settings2 = $this->settings_service->get_settings( 'paket' );
		
		$this->assertEquals( $settings1, $settings2 );
		
		// Force refresh should bypass cache
		update_option( 'woocommerce_pr_dhl_paket_settings', array( 'title' => 'Updated Title' ) );
		$settings3 = $this->settings_service->get_settings( 'paket', true );
		
		$this->assertEquals( 'Updated Title', $settings3['title'] );
	}

	/**
	 * Test backward compatibility method
	 */
	public function test_backward_compatibility() {
		// Set Germany as base country
		update_option( 'woocommerce_default_country', 'DE:' );
		
		$settings = $this->settings_service->get_shipping_dhl_settings();
		
		$this->assertIsArray( $settings );
		$this->assertArrayHasKey( 'enabled', $settings );
		$this->assertArrayHasKey( 'title', $settings );
	}

	/**
	 * Test invalid service handling
	 */
	public function test_invalid_service() {
		$settings = $this->settings_service->get_settings( 'invalid_service' );
		$this->assertEmpty( $settings );
		
		$result = $this->settings_service->update_settings( 'invalid_service', array() );
		$this->assertFalse( $result );
	}

	/**
	 * Test cache clearing
	 */
	public function test_cache_clearing() {
		// Load settings to cache them
		$this->settings_service->get_settings( 'paket' );
		
		// Clear cache
		$this->settings_service->clear_settings_cache();
		
		// This should work without issues
		$settings = $this->settings_service->get_settings( 'paket' );
		$this->assertIsArray( $settings );
	}
}
