<?php
/**
 * Test integration of new services with existing code
 *
 * @package DHL_For_WooCommerce
 */

class Test_Integration extends DHL_Unit_Test_Case {

	/**
	 * Test that new service container works
	 */
	public function test_service_container_integration() {
		// Test that service container is available
		$this->assertTrue( function_exists( 'pr_dhl_container' ) );
		$this->assertTrue( function_exists( 'pr_dhl_resolve' ) );
		
		$container = pr_dhl_container();
		$this->assertInstanceOf( 'PR_DHL_Service_Container', $container );
	}

	/**
	 * Test that settings service integration works
	 */
	public function test_settings_service_integration() {
		$plugin = PR_DHL();
		
		// Test that new method exists
		$this->assertTrue( method_exists( $plugin, 'get_settings_service' ) );
		
		// Test that it returns a settings service
		$settings_service = $plugin->get_settings_service();
		$this->assertInstanceOf( 'PR_DHL_Settings_Service', $settings_service );
	}

	/**
	 * Test that existing settings method still works
	 */
	public function test_existing_settings_method_compatibility() {
		$plugin = PR_DHL();
		
		// This should still work as before
		$settings = $plugin->get_shipping_dhl_settings();
		$this->assertIsArray( $settings );
	}

	/**
	 * Test that new settings service provides same data as old method
	 */
	public function test_settings_data_consistency() {
		$plugin = PR_DHL();
		$settings_service = $plugin->get_settings_service();
		
		// Get settings from both old and new methods
		$old_settings = $plugin->get_shipping_dhl_settings();
		$new_settings = $settings_service->get_shipping_dhl_settings();
		
		// They should contain the same basic structure
		$this->assertIsArray( $old_settings );
		$this->assertIsArray( $new_settings );
		
		// Both should have enabled key
		if ( ! empty( $old_settings ) ) {
			$this->assertArrayHasKey( 'enabled', $old_settings );
		}
		if ( ! empty( $new_settings ) ) {
			$this->assertArrayHasKey( 'enabled', $new_settings );
		}
	}

	/**
	 * Test that service container singleton works
	 */
	public function test_service_container_singleton() {
		$container1 = pr_dhl_container();
		$container2 = pr_dhl_container();
		
		$this->assertSame( $container1, $container2 );
	}

	/**
	 * Test that services can be resolved
	 */
	public function test_service_resolution() {
		$container = pr_dhl_container();
		
		// Test that core services are available
		$this->assertTrue( $container->has( 'logger' ) );
		$this->assertTrue( $container->has( 'settings' ) );
		
		// Test that they can be resolved
		$logger = $container->resolve( 'logger' );
		$this->assertInstanceOf( 'PR_DHL_Logger', $logger );
		
		$settings = $container->resolve( 'settings' );
		$this->assertInstanceOf( 'PR_DHL_Settings_Service', $settings );
	}

	/**
	 * Test that invalid service throws exception
	 */
	public function test_invalid_service_exception() {
		$container = pr_dhl_container();
		
		$this->expectException( Exception::class );
		$container->resolve( 'nonexistent_service' );
	}

	/**
	 * Test that existing hooks still work
	 */
	public function test_existing_hooks_still_work() {
		$plugin = PR_DHL();
		
		// Test that critical hooks are still registered
		$this->assertGreaterThan( 0, has_action( 'init', array( $plugin, 'init' ) ) );
		$this->assertGreaterThan( 0, has_action( 'woocommerce_shipping_init', array( $plugin, 'includes' ) ) );
		$this->assertGreaterThan( 0, has_filter( 'woocommerce_shipping_methods', array( $plugin, 'add_shipping_method' ) ) );
	}

	/**
	 * Test that existing AJAX callbacks still work
	 */
	public function test_existing_ajax_callbacks() {
		$plugin = PR_DHL();
		
		// Test that AJAX actions are still registered
		$this->assertGreaterThan( 0, has_action( 'wp_ajax_test_dhl_connection', array( $plugin, 'test_dhl_connection_callback' ) ) );
		$this->assertGreaterThan( 0, has_action( 'wp_ajax_dhl_get_myaccount', array( $plugin, 'dhl_get_myaccount_callback' ) ) );
	}

	/**
	 * Test that constants are still defined
	 */
	public function test_constants_still_defined() {
		$required_constants = array(
			'PR_DHL_PLUGIN_FILE',
			'PR_DHL_PLUGIN_BASENAME',
			'PR_DHL_PLUGIN_DIR_PATH',
			'PR_DHL_PLUGIN_DIR_URL',
			'PR_DHL_VERSION',
		);

		foreach ( $required_constants as $constant ) {
			$this->assertTrue( defined( $constant ), "Constant {$constant} should still be defined" );
		}
	}

	/**
	 * Test that factory method still works
	 */
	public function test_factory_method_compatibility() {
		$plugin = PR_DHL();
		
		// This should still work
		$dhl_obj = $plugin->get_dhl_factory();
		$this->assertNotNull( $dhl_obj );
		
		// Should still have the same methods
		$this->assertTrue( method_exists( $dhl_obj, 'is_dhl_paket' ) );
		$this->assertTrue( method_exists( $dhl_obj, 'is_dhl_deutsche_post' ) );
	}
}
