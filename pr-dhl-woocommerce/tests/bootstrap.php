<?php
/**
 * PHPUnit bootstrap file for DHL WooCommerce Plugin
 *
 * @package DHL_For_WooCommerce
 */

// Composer autoloader must be loaded before WP_PHPUNIT__DIR will be available
require_once dirname( __DIR__ ) . '/vendor/autoload.php';

// Give access to tests_add_filter() function.
require_once getenv( 'WP_PHPUNIT_DIR' ) . '/includes/functions.php';

/**
 * Manually load the plugin being tested.
 */
function _manually_load_plugin() {
	// Load WooCommerce first
	require_once dirname( __DIR__ ) . '/wp-content/plugins/woocommerce/woocommerce.php';
	
	// Load our plugin
	require_once dirname( __DIR__ ) . '/pr-dhl-woocommerce.php';
}

tests_add_filter( 'muplugins_loaded', '_manually_load_plugin' );

// Start up the WP testing environment.
require getenv( 'WP_PHPUNIT_DIR' ) . '/includes/bootstrap.php';

// Include WooCommerce testing framework
require_once dirname( __DIR__ ) . '/wp-content/plugins/woocommerce/tests/legacy/framework/class-wc-unit-test-case.php';

/**
 * Base test case for DHL plugin tests
 */
abstract class DHL_Unit_Test_Case extends WC_Unit_Test_Case {
	
	/**
	 * Setup test case
	 */
	public function setUp(): void {
		parent::setUp();
		
		// Ensure WooCommerce is active
		if ( ! class_exists( 'WooCommerce' ) ) {
			$this->markTestSkipped( 'WooCommerce is not active' );
		}
		
		// Set up DHL settings for testing
		$this->setup_dhl_test_settings();
	}
	
	/**
	 * Setup basic DHL settings for testing
	 */
	protected function setup_dhl_test_settings() {
		// Set Germany as base country for DHL Paket testing
		update_option( 'woocommerce_default_country', 'DE:' );
		
		// Basic DHL Paket settings
		update_option( 'woocommerce_pr_dhl_paket_settings', array(
			'enabled' => 'yes',
			'title' => 'DHL Paket',
			'sandbox' => 'yes', // Always use sandbox for tests
		) );
	}
	
	/**
	 * Create a test order with German address
	 */
	protected function create_test_order() {
		$order = wc_create_order();
		
		$order->set_billing_address( array(
			'first_name' => 'Test',
			'last_name'  => 'Customer',
			'company'    => '',
			'address_1'  => 'Teststraße 1',
			'address_2'  => '',
			'city'       => 'Berlin',
			'state'      => '',
			'postcode'   => '10115',
			'country'    => 'DE',
			'email'      => '<EMAIL>',
			'phone'      => '030123456789',
		) );
		
		$order->set_shipping_address( array(
			'first_name' => 'Test',
			'last_name'  => 'Customer',
			'company'    => '',
			'address_1'  => 'Teststraße 1',
			'address_2'  => '',
			'city'       => 'Berlin',
			'state'      => '',
			'postcode'   => '10115',
			'country'    => 'DE',
		) );
		
		$order->save();
		
		return $order;
	}
	
	/**
	 * Create a test product
	 */
	protected function create_test_product() {
		$product = new WC_Product_Simple();
		$product->set_name( 'Test Product' );
		$product->set_regular_price( 10 );
		$product->set_weight( 1 );
		$product->set_length( 10 );
		$product->set_width( 10 );
		$product->set_height( 10 );
		$product->save();
		
		return $product;
	}
}
