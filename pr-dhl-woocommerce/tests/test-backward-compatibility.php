<?php
/**
 * Test backward compatibility
 *
 * @package DHL_For_WooCommerce
 */

class Test_Backward_Compatibility extends DHL_Unit_Test_Case {

	/**
	 * Test that main plugin class exists and is accessible
	 */
	public function test_main_plugin_class_exists() {
		$this->assertTrue( class_exists( 'PR_DHL_WC' ) );
		$this->assertInstanceOf( 'PR_DHL_WC', PR_DHL() );
	}

	/**
	 * Test that singleton pattern works
	 */
	public function test_singleton_pattern() {
		$instance1 = PR_DHL();
		$instance2 = PR_DHL();
		
		$this->assertSame( $instance1, $instance2 );
	}

	/**
	 * Test that factory method works
	 */
	public function test_dhl_factory() {
		$plugin = PR_DHL();
		
		// Should not throw exception for Germany
		$dhl_obj = $plugin->get_dhl_factory();
		$this->assertNotNull( $dhl_obj );
		$this->assertTrue( $dhl_obj->is_dhl_paket() );
	}

	/**
	 * Test that shipping methods are registered
	 */
	public function test_shipping_methods_registered() {
		$shipping_methods = WC()->shipping()->get_shipping_methods();
		
		$this->assertArrayHasKey( 'pr_dhl_paket', $shipping_methods );
		$this->assertInstanceOf( 'PR_DHL_WC_Method_Paket', $shipping_methods['pr_dhl_paket'] );
	}

	/**
	 * Test that order meta box is added
	 */
	public function test_order_meta_box() {
		global $wp_meta_boxes;
		
		// Simulate order edit screen
		set_current_screen( 'shop_order' );
		
		$order = $this->create_test_order();
		
		// Trigger meta box addition
		do_action( 'add_meta_boxes', 'shop_order', $order );
		
		// Check if DHL meta box is registered
		$screen_id = 'shop_order';
		$this->assertArrayHasKey( 'woocommerce-shipment-dhl-label', $wp_meta_boxes[ $screen_id ]['side']['high'] );
	}

	/**
	 * Test that constants are defined
	 */
	public function test_constants_defined() {
		$required_constants = array(
			'PR_DHL_PLUGIN_FILE',
			'PR_DHL_PLUGIN_BASENAME',
			'PR_DHL_PLUGIN_DIR_PATH',
			'PR_DHL_PLUGIN_DIR_URL',
			'PR_DHL_VERSION',
		);

		foreach ( $required_constants as $constant ) {
			$this->assertTrue( defined( $constant ), "Constant {$constant} should be defined" );
		}
	}

	/**
	 * Test that hooks are properly registered
	 */
	public function test_hooks_registered() {
		$plugin = PR_DHL();
		
		// Test that init hook is registered
		$this->assertGreaterThan( 0, has_action( 'init', array( $plugin, 'init' ) ) );
		
		// Test that shipping init hook is registered
		$this->assertGreaterThan( 0, has_action( 'woocommerce_shipping_init', array( $plugin, 'includes' ) ) );
		
		// Test that shipping methods filter is registered
		$this->assertGreaterThan( 0, has_filter( 'woocommerce_shipping_methods', array( $plugin, 'add_shipping_method' ) ) );
	}

	/**
	 * Test that AJAX actions are registered
	 */
	public function test_ajax_actions_registered() {
		$plugin = PR_DHL();
		
		// Test connection AJAX
		$this->assertGreaterThan( 0, has_action( 'wp_ajax_test_dhl_connection', array( $plugin, 'test_dhl_connection_callback' ) ) );
		
		// MyAccount AJAX
		$this->assertGreaterThan( 0, has_action( 'wp_ajax_dhl_get_myaccount', array( $plugin, 'dhl_get_myaccount_callback' ) ) );
	}

	/**
	 * Test that textdomain is loaded
	 */
	public function test_textdomain_loaded() {
		// Trigger textdomain loading
		do_action( 'init' );
		
		// Test that a known translation string works
		$translated = __( 'Test Connection', 'dhl-for-woocommerce' );
		$this->assertNotEmpty( $translated );
	}

	/**
	 * Test that WooCommerce compatibility is declared
	 */
	public function test_woocommerce_compatibility() {
		if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
			$compatible_features = \Automattic\WooCommerce\Utilities\FeaturesUtil::get_compatible_features_for_plugin( 
				plugin_basename( PR_DHL_PLUGIN_FILE ) 
			);
			
			$this->assertContains( 'custom_order_tables', $compatible_features );
			$this->assertContains( 'product_block_editor', $compatible_features );
		}
	}
}
