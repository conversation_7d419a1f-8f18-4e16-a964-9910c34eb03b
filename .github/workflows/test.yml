name: Test

on:
  push:
    branches: [ master, develop ]
  pull_request:
    branches: [ master, develop ]

jobs:
  phpunit:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        php-version: ['7.4', '8.0', '8.1', '8.2']
        wordpress-version: ['6.6', '6.7', '6.8']
        woocommerce-version: ['9.8', '10.0']
        
    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: wordpress_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, mysql, mysqli, pdo_mysql, bcmath, soap, intl, gd, exif, iconv
        coverage: none

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ matrix.php-version }}-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-${{ matrix.php-version }}-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Install WordPress Test Suite
      run: |
        bash bin/install-wp-tests.sh wordpress_test root root localhost:3306 ${{ matrix.wordpress-version }}

    - name: Install WooCommerce
      run: |
        cd /tmp/wordpress/wp-content/plugins
        wget https://downloads.wordpress.org/plugin/woocommerce.${{ matrix.woocommerce-version }}.zip
        unzip woocommerce.${{ matrix.woocommerce-version }}.zip

    - name: Run PHPUnit tests
      run: vendor/bin/phpunit
      env:
        WP_PHPUNIT_DIR: /tmp/wordpress-tests-lib

  phpcs:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        tools: composer, cs2pr

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-8.1-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-8.1-

    - name: Install dependencies
      run: |
        composer install --prefer-dist --no-progress
        composer global require "squizlabs/php_codesniffer=*"
        composer global require "wp-coding-standards/wpcs"
        composer global require "woocommerce/woocommerce-sniffs"
        composer global require "phpcompatibility/phpcompatibility-wp"

    - name: Configure PHPCS
      run: |
        ~/.composer/vendor/bin/phpcs --config-set installed_paths ~/.composer/vendor/wp-coding-standards/wpcs,~/.composer/vendor/woocommerce/woocommerce-sniffs,~/.composer/vendor/phpcompatibility/phpcompatibility-wp

    - name: Run PHPCS
      run: ~/.composer/vendor/bin/phpcs --report=checkstyle | cs2pr

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-8.1-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-8.1-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Run security audit
      run: composer audit

  compatibility:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'

    - name: Install PHPCompatibility
      run: |
        composer global require "phpcompatibility/php-compatibility=*"
        ~/.composer/vendor/bin/phpcs --config-set installed_paths ~/.composer/vendor/phpcompatibility/php-compatibility

    - name: Check PHP compatibility
      run: ~/.composer/vendor/bin/phpcs --standard=PHPCompatibility --runtime-set testVersion 7.4- --ignore=vendor/,node_modules/,tests/ .
